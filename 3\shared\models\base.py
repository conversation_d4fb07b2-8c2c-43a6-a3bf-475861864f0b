"""
Base database models and configurations for the gaming platform.
Provides common functionality and patterns for all database models.
"""

from datetime import datetime
from typing import Any, Dict, Optional
from uuid import UUID, uuid4

from sqlalchemy import Column, DateTime, String, Boolean, Text, Integer
from sqlalchemy.dialects.postgresql import UUID as PostgresUUID
from sqlalchemy.ext.declarative import declarative_base, declared_attr
from sqlalchemy.orm import Session
from sqlalchemy.sql import func
from pydantic import BaseModel, Field, ConfigDict


# SQLAlchemy Base
class CustomBase:
    """Custom base class with common functionality for all models."""
    
    @declared_attr
    def __tablename__(cls) -> str:
        """Generate table name from class name."""
        return cls.__name__.lower() + 's'
    
    # Primary key
    id: UUID = Column(
        PostgresUUID(as_uuid=True),
        primary_key=True,
        default=uuid4,
        index=True
    )
    
    # Timestamps
    created_at: datetime = Column(
        DateTime(timezone=True),
        server_default=func.now(),
        nullable=False,
        index=True
    )
    
    updated_at: datetime = Column(
        DateTime(timezone=True),
        server_default=func.now(),
        onupdate=func.now(),
        nullable=False,
        index=True
    )
    
    # Soft delete
    is_deleted: bool = Column(Boolean, default=False, nullable=False, index=True)
    deleted_at: Optional[datetime] = Column(DateTime(timezone=True), nullable=True)
    
    # Metadata
    metadata_: Optional[Dict[str, Any]] = Column(Text, nullable=True)
    version: int = Column(Integer, default=1, nullable=False)
    
    def soft_delete(self, session: Session) -> None:
        """Soft delete the record."""
        self.is_deleted = True
        self.deleted_at = datetime.utcnow()
        session.commit()
    
    def restore(self, session: Session) -> None:
        """Restore a soft-deleted record."""
        self.is_deleted = False
        self.deleted_at = None
        session.commit()
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert model to dictionary."""
        return {
            column.name: getattr(self, column.name)
            for column in self.__table__.columns
        }
    
    def update_from_dict(self, data: Dict[str, Any]) -> None:
        """Update model from dictionary."""
        for key, value in data.items():
            if hasattr(self, key):
                setattr(self, key, value)
        self.version += 1


Base = declarative_base(cls=CustomBase)


# Pydantic Base Models
class BaseSchema(BaseModel):
    """Base Pydantic model with common configuration."""
    
    model_config = ConfigDict(
        from_attributes=True,
        validate_assignment=True,
        arbitrary_types_allowed=True,
        str_strip_whitespace=True,
        json_encoders={
            datetime: lambda v: v.isoformat(),
            UUID: lambda v: str(v)
        }
    )


class BaseCreateSchema(BaseSchema):
    """Base schema for creating records."""
    pass


class BaseUpdateSchema(BaseSchema):
    """Base schema for updating records."""
    pass


class BaseResponseSchema(BaseSchema):
    """Base schema for API responses."""
    
    id: UUID = Field(..., description="Unique identifier")
    created_at: datetime = Field(..., description="Creation timestamp")
    updated_at: datetime = Field(..., description="Last update timestamp")
    version: int = Field(..., description="Record version for optimistic locking")


class PaginationSchema(BaseSchema):
    """Schema for pagination parameters."""
    
    page: int = Field(1, ge=1, description="Page number")
    size: int = Field(20, ge=1, le=100, description="Items per page")
    sort_by: Optional[str] = Field(None, description="Sort field")
    sort_order: Optional[str] = Field("asc", regex="^(asc|desc)$", description="Sort order")


class PaginatedResponseSchema(BaseSchema):
    """Schema for paginated API responses."""
    
    items: list = Field(..., description="List of items")
    total: int = Field(..., description="Total number of items")
    page: int = Field(..., description="Current page number")
    size: int = Field(..., description="Items per page")
    pages: int = Field(..., description="Total number of pages")
    has_next: bool = Field(..., description="Whether there is a next page")
    has_prev: bool = Field(..., description="Whether there is a previous page")


class ErrorSchema(BaseSchema):
    """Schema for error responses."""
    
    error: str = Field(..., description="Error type")
    message: str = Field(..., description="Error message")
    details: Optional[Dict[str, Any]] = Field(None, description="Additional error details")
    timestamp: datetime = Field(default_factory=datetime.utcnow, description="Error timestamp")
    request_id: Optional[str] = Field(None, description="Request ID for tracking")


class HealthCheckSchema(BaseSchema):
    """Schema for health check responses."""
    
    status: str = Field(..., description="Service status")
    timestamp: datetime = Field(default_factory=datetime.utcnow, description="Check timestamp")
    version: str = Field(..., description="Service version")
    dependencies: Dict[str, str] = Field(..., description="Dependency statuses")


class MetricsSchema(BaseSchema):
    """Schema for metrics responses."""
    
    name: str = Field(..., description="Metric name")
    value: float = Field(..., description="Metric value")
    unit: str = Field(..., description="Metric unit")
    timestamp: datetime = Field(default_factory=datetime.utcnow, description="Metric timestamp")
    labels: Optional[Dict[str, str]] = Field(None, description="Metric labels")


# Audit Log Base
class AuditLogMixin:
    """Mixin for audit logging functionality."""
    
    # Audit fields
    created_by: Optional[UUID] = Column(PostgresUUID(as_uuid=True), nullable=True, index=True)
    updated_by: Optional[UUID] = Column(PostgresUUID(as_uuid=True), nullable=True, index=True)
    
    # IP tracking
    created_ip: Optional[str] = Column(String(45), nullable=True)  # IPv6 support
    updated_ip: Optional[str] = Column(String(45), nullable=True)
    
    # User agent tracking
    created_user_agent: Optional[str] = Column(Text, nullable=True)
    updated_user_agent: Optional[str] = Column(Text, nullable=True)


# Enum Base
class BaseEnum:
    """Base class for enum-like constants."""
    
    @classmethod
    def values(cls) -> list:
        """Get all enum values."""
        return [
            getattr(cls, attr) for attr in dir(cls)
            if not attr.startswith('_') and not callable(getattr(cls, attr))
        ]
    
    @classmethod
    def choices(cls) -> list:
        """Get choices for form fields."""
        return [(value, value.replace('_', ' ').title()) for value in cls.values()]


# Status Enums
class RecordStatus(BaseEnum):
    """Common record status values."""
    ACTIVE = "active"
    INACTIVE = "inactive"
    PENDING = "pending"
    SUSPENDED = "suspended"
    ARCHIVED = "archived"


class GameStatus(BaseEnum):
    """Game status values."""
    WAITING = "waiting"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    CANCELLED = "cancelled"
    ABANDONED = "abandoned"


class UserStatus(BaseEnum):
    """User status values."""
    ACTIVE = "active"
    INACTIVE = "inactive"
    SUSPENDED = "suspended"
    BANNED = "banned"
    PENDING_VERIFICATION = "pending_verification"


class MatchmakingStatus(BaseEnum):
    """Matchmaking status values."""
    QUEUED = "queued"
    MATCHED = "matched"
    CANCELLED = "cancelled"
    TIMEOUT = "timeout"


# Common Validators
def validate_uuid(value: str) -> UUID:
    """Validate UUID string."""
    try:
        return UUID(value)
    except ValueError:
        raise ValueError("Invalid UUID format")


def validate_email(value: str) -> str:
    """Validate email format."""
    import re
    pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    if not re.match(pattern, value):
        raise ValueError("Invalid email format")
    return value.lower()


def validate_password_strength(value: str) -> str:
    """Validate password strength."""
    if len(value) < 8:
        raise ValueError("Password must be at least 8 characters long")
    
    if not any(c.isupper() for c in value):
        raise ValueError("Password must contain at least one uppercase letter")
    
    if not any(c.islower() for c in value):
        raise ValueError("Password must contain at least one lowercase letter")
    
    if not any(c.isdigit() for c in value):
        raise ValueError("Password must contain at least one digit")
    
    if not any(c in "!@#$%^&*()_+-=[]{}|;:,.<>?" for c in value):
        raise ValueError("Password must contain at least one special character")
    
    return value


# Database Utilities
class DatabaseMixin:
    """Mixin with common database operations."""
    
    @classmethod
    def get_by_id(cls, session: Session, id: UUID):
        """Get record by ID."""
        return session.query(cls).filter(
            cls.id == id,
            cls.is_deleted == False
        ).first()
    
    @classmethod
    def get_all(cls, session: Session, skip: int = 0, limit: int = 100):
        """Get all records with pagination."""
        return session.query(cls).filter(
            cls.is_deleted == False
        ).offset(skip).limit(limit).all()
    
    @classmethod
    def count(cls, session: Session) -> int:
        """Count all active records."""
        return session.query(cls).filter(cls.is_deleted == False).count()
    
    def save(self, session: Session):
        """Save record to database."""
        session.add(self)
        session.commit()
        session.refresh(self)
        return self
    
    def delete(self, session: Session):
        """Hard delete record."""
        session.delete(self)
        session.commit()


# Export all
__all__ = [
    'Base',
    'BaseSchema',
    'BaseCreateSchema',
    'BaseUpdateSchema',
    'BaseResponseSchema',
    'PaginationSchema',
    'PaginatedResponseSchema',
    'ErrorSchema',
    'HealthCheckSchema',
    'MetricsSchema',
    'AuditLogMixin',
    'BaseEnum',
    'RecordStatus',
    'GameStatus',
    'UserStatus',
    'MatchmakingStatus',
    'validate_uuid',
    'validate_email',
    'validate_password_strength',
    'DatabaseMixin'
]
