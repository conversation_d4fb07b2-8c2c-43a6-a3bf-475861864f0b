"""
User Models - Database and Pydantic models for user management.
Handles user accounts, profiles, authentication, and social features.
"""

from datetime import datetime
from typing import Optional, List, Dict, Any
from uuid import UUID
from enum import Enum

from sqlalchemy import Column, String, Boolean, Integer, DateTime, Text, ForeignKey, Table
from sqlalchemy.dialects.postgresql import UUID as PostgresUUID, JSONB
from sqlalchemy.orm import relationship
from pydantic import BaseModel, Field, EmailStr, validator

from .base import Base, BaseSchema, BaseCreateSchema, BaseUpdateSchema, BaseResponseSchema, AuditLogMixin


class UserRole(str, Enum):
    """User role enumeration."""
    USER = "user"
    MODERATOR = "moderator"
    ADMIN = "admin"
    SUPER_ADMIN = "super_admin"


class UserStatus(str, Enum):
    """User status enumeration."""
    ACTIVE = "active"
    INACTIVE = "inactive"
    SUSPENDED = "suspended"
    BANNED = "banned"
    PENDING_VERIFICATION = "pending_verification"


class AuthProvider(str, Enum):
    """Authentication provider enumeration."""
    LOCAL = "local"
    GOOGLE = "google"
    FACEBOOK = "facebook"
    GITHUB = "github"


# Association table for user friendships
user_friends = Table(
    'user_friends',
    Base.metadata,
    Column('user_id', PostgresUUID(as_uuid=True), ForeignKey('users.id'), primary_key=True),
    Column('friend_id', PostgresUUID(as_uuid=True), ForeignKey('users.id'), primary_key=True),
    Column('created_at', DateTime(timezone=True), default=datetime.utcnow),
    Column('status', String(20), default='pending')  # pending, accepted, blocked
)


class User(Base, AuditLogMixin):
    """User database model."""
    
    __tablename__ = "users"
    
    # Basic Information
    username = Column(String(50), unique=True, nullable=False, index=True)
    email = Column(String(255), unique=True, nullable=False, index=True)
    email_verified = Column(Boolean, default=False, nullable=False)
    
    # Authentication
    password_hash = Column(String(255), nullable=True)  # Nullable for OAuth users
    auth_provider = Column(String(20), default=AuthProvider.LOCAL, nullable=False)
    provider_id = Column(String(255), nullable=True)  # External provider ID
    
    # Profile Information
    first_name = Column(String(100), nullable=True)
    last_name = Column(String(100), nullable=True)
    display_name = Column(String(100), nullable=True)
    avatar_url = Column(String(500), nullable=True)
    bio = Column(Text, nullable=True)
    
    # Status and Permissions
    role = Column(String(20), default=UserRole.USER, nullable=False)
    status = Column(String(30), default=UserStatus.PENDING_VERIFICATION, nullable=False)
    
    # Gaming Statistics
    total_games = Column(Integer, default=0, nullable=False)
    games_won = Column(Integer, default=0, nullable=False)
    games_lost = Column(Integer, default=0, nullable=False)
    games_drawn = Column(Integer, default=0, nullable=False)
    
    # ELO Ratings (per game type)
    elo_ratings = Column(JSONB, default=dict, nullable=False)
    
    # Account Security
    failed_login_attempts = Column(Integer, default=0, nullable=False)
    locked_until = Column(DateTime(timezone=True), nullable=True)
    last_login = Column(DateTime(timezone=True), nullable=True)
    last_activity = Column(DateTime(timezone=True), nullable=True)
    
    # Privacy Settings
    privacy_settings = Column(JSONB, default=dict, nullable=False)
    
    # Preferences
    preferences = Column(JSONB, default=dict, nullable=False)
    
    # GDPR Compliance
    data_processing_consent = Column(Boolean, default=False, nullable=False)
    marketing_consent = Column(Boolean, default=False, nullable=False)
    data_retention_until = Column(DateTime(timezone=True), nullable=True)
    
    # Relationships
    friends = relationship(
        "User",
        secondary=user_friends,
        primaryjoin=id == user_friends.c.user_id,
        secondaryjoin=id == user_friends.c.friend_id,
        back_populates="friends"
    )
    
    # Game relationships (defined in game models)
    games_as_player1 = relationship("Game", foreign_keys="Game.player1_id", back_populates="player1")
    games_as_player2 = relationship("Game", foreign_keys="Game.player2_id", back_populates="player2")
    
    @property
    def win_rate(self) -> float:
        """Calculate win rate percentage."""
        if self.total_games == 0:
            return 0.0
        return (self.games_won / self.total_games) * 100
    
    @property
    def full_name(self) -> str:
        """Get full name."""
        if self.first_name and self.last_name:
            return f"{self.first_name} {self.last_name}"
        return self.display_name or self.username
    
    def get_elo_rating(self, game_type: str) -> int:
        """Get ELO rating for specific game type."""
        return self.elo_ratings.get(game_type, 1200)  # Default ELO
    
    def update_elo_rating(self, game_type: str, new_rating: int):
        """Update ELO rating for specific game type."""
        if self.elo_ratings is None:
            self.elo_ratings = {}
        self.elo_ratings[game_type] = new_rating
    
    def is_friend_with(self, user_id: UUID) -> bool:
        """Check if user is friends with another user."""
        return any(friend.id == user_id for friend in self.friends)
    
    def can_play_game(self) -> bool:
        """Check if user can play games."""
        return self.status == UserStatus.ACTIVE and not self.is_locked()
    
    def is_locked(self) -> bool:
        """Check if account is locked."""
        if self.locked_until is None:
            return False
        return datetime.utcnow() < self.locked_until
    
    def lock_account(self, duration_minutes: int = 15):
        """Lock account for specified duration."""
        self.locked_until = datetime.utcnow() + timedelta(minutes=duration_minutes)
    
    def unlock_account(self):
        """Unlock account."""
        self.locked_until = None
        self.failed_login_attempts = 0


class UserSession(Base):
    """User session model for tracking active sessions."""
    
    __tablename__ = "user_sessions"
    
    user_id = Column(PostgresUUID(as_uuid=True), ForeignKey('users.id'), nullable=False, index=True)
    session_token = Column(String(255), unique=True, nullable=False, index=True)
    refresh_token = Column(String(255), unique=True, nullable=True, index=True)
    
    # Session Information
    ip_address = Column(String(45), nullable=True)  # IPv6 support
    user_agent = Column(Text, nullable=True)
    device_info = Column(JSONB, nullable=True)
    
    # Timestamps
    expires_at = Column(DateTime(timezone=True), nullable=False)
    last_activity = Column(DateTime(timezone=True), default=datetime.utcnow, nullable=False)
    
    # Status
    is_active = Column(Boolean, default=True, nullable=False)
    revoked_at = Column(DateTime(timezone=True), nullable=True)
    
    # Relationships
    user = relationship("User", backref="sessions")
    
    def is_expired(self) -> bool:
        """Check if session is expired."""
        return datetime.utcnow() > self.expires_at
    
    def revoke(self):
        """Revoke session."""
        self.is_active = False
        self.revoked_at = datetime.utcnow()


# Pydantic Schemas

class UserCreateSchema(BaseCreateSchema):
    """Schema for creating a new user."""
    
    username: str = Field(..., min_length=3, max_length=50, regex="^[a-zA-Z0-9_-]+$")
    email: EmailStr = Field(...)
    password: str = Field(..., min_length=8, max_length=128)
    first_name: Optional[str] = Field(None, max_length=100)
    last_name: Optional[str] = Field(None, max_length=100)
    display_name: Optional[str] = Field(None, max_length=100)
    
    @validator('password')
    def validate_password(cls, v):
        """Validate password strength."""
        if len(v) < 8:
            raise ValueError('Password must be at least 8 characters long')
        if not any(c.isupper() for c in v):
            raise ValueError('Password must contain at least one uppercase letter')
        if not any(c.islower() for c in v):
            raise ValueError('Password must contain at least one lowercase letter')
        if not any(c.isdigit() for c in v):
            raise ValueError('Password must contain at least one digit')
        return v


class UserUpdateSchema(BaseUpdateSchema):
    """Schema for updating user information."""
    
    first_name: Optional[str] = Field(None, max_length=100)
    last_name: Optional[str] = Field(None, max_length=100)
    display_name: Optional[str] = Field(None, max_length=100)
    bio: Optional[str] = Field(None, max_length=500)
    avatar_url: Optional[str] = Field(None, max_length=500)
    privacy_settings: Optional[Dict[str, Any]] = Field(None)
    preferences: Optional[Dict[str, Any]] = Field(None)


class UserResponseSchema(BaseResponseSchema):
    """Schema for user API responses."""
    
    username: str
    email: str
    email_verified: bool
    first_name: Optional[str]
    last_name: Optional[str]
    display_name: Optional[str]
    avatar_url: Optional[str]
    bio: Optional[str]
    role: UserRole
    status: UserStatus
    total_games: int
    games_won: int
    games_lost: int
    games_drawn: int
    win_rate: float
    elo_ratings: Dict[str, int]
    last_login: Optional[datetime]
    last_activity: Optional[datetime]


class UserPublicSchema(BaseSchema):
    """Schema for public user information."""
    
    id: UUID
    username: str
    display_name: Optional[str]
    avatar_url: Optional[str]
    total_games: int
    games_won: int
    win_rate: float
    elo_ratings: Dict[str, int]


class UserLoginSchema(BaseSchema):
    """Schema for user login."""
    
    email: EmailStr = Field(...)
    password: str = Field(..., min_length=1)
    remember_me: bool = Field(default=False)


class UserRegistrationSchema(UserCreateSchema):
    """Schema for user registration."""
    
    terms_accepted: bool = Field(..., description="User must accept terms of service")
    privacy_accepted: bool = Field(..., description="User must accept privacy policy")
    
    @validator('terms_accepted')
    def validate_terms_accepted(cls, v):
        """Validate terms acceptance."""
        if not v:
            raise ValueError('Terms of service must be accepted')
        return v
    
    @validator('privacy_accepted')
    def validate_privacy_accepted(cls, v):
        """Validate privacy policy acceptance."""
        if not v:
            raise ValueError('Privacy policy must be accepted')
        return v


class PasswordChangeSchema(BaseSchema):
    """Schema for password change."""
    
    current_password: str = Field(...)
    new_password: str = Field(..., min_length=8, max_length=128)
    confirm_password: str = Field(...)
    
    @validator('confirm_password')
    def passwords_match(cls, v, values):
        """Validate password confirmation."""
        if 'new_password' in values and v != values['new_password']:
            raise ValueError('Passwords do not match')
        return v


class PasswordResetRequestSchema(BaseSchema):
    """Schema for password reset request."""
    
    email: EmailStr = Field(...)


class PasswordResetSchema(BaseSchema):
    """Schema for password reset."""
    
    token: str = Field(...)
    new_password: str = Field(..., min_length=8, max_length=128)
    confirm_password: str = Field(...)
    
    @validator('confirm_password')
    def passwords_match(cls, v, values):
        """Validate password confirmation."""
        if 'new_password' in values and v != values['new_password']:
            raise ValueError('Passwords do not match')
        return v


class FriendRequestSchema(BaseSchema):
    """Schema for friend requests."""
    
    friend_id: UUID = Field(...)


class UserStatsSchema(BaseSchema):
    """Schema for user statistics."""
    
    total_games: int
    games_won: int
    games_lost: int
    games_drawn: int
    win_rate: float
    elo_ratings: Dict[str, int]
    favorite_game: Optional[str]
    average_game_duration: Optional[float]
    longest_win_streak: int
    current_win_streak: int
