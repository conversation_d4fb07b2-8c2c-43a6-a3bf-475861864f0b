# Database Configuration
DATABASE_URL=postgresql://gaming_user:gaming_password@localhost:5432/gaming_platform
DATABASE_HOST=localhost
DATABASE_PORT=5432
DATABASE_NAME=gaming_platform
DATABASE_USER=gaming_user
DATABASE_PASSWORD=gaming_password
DATABASE_POOL_SIZE=20
DATABASE_MAX_OVERFLOW=30

# Redis Configuration
REDIS_URL=redis://localhost:6379
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=0
REDIS_PASSWORD=
REDIS_MAX_CONNECTIONS=100

# RabbitMQ Configuration
RABBITMQ_URL=amqp://gaming_user:gaming_password@localhost:5672/
RABBITMQ_HOST=localhost
RABBITMQ_PORT=5672
RABBITMQ_USER=gaming_user
RABBITMQ_PASSWORD=gaming_password
RABBITMQ_VHOST=/

# JWT Configuration
JWT_SECRET_KEY=your-super-secret-jwt-key-change-in-production-minimum-32-characters
JWT_ALGORITHM=HS256
JWT_ACCESS_TOKEN_EXPIRE_MINUTES=30
JWT_REFRESH_TOKEN_EXPIRE_DAYS=7
JWT_ISSUER=gaming-platform
JWT_AUDIENCE=gaming-platform-users

# OAuth Configuration
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret
FACEBOOK_CLIENT_ID=your-facebook-client-id
FACEBOOK_CLIENT_SECRET=your-facebook-client-secret
GITHUB_CLIENT_ID=your-github-client-id
GITHUB_CLIENT_SECRET=your-github-client-secret

# Service URLs
AUTH_SERVICE_URL=http://localhost:8001
GAME_ENGINE_URL=http://localhost:8002
MATCHMAKING_URL=http://localhost:8003
CHAT_SERVICE_URL=http://localhost:8004
USER_MANAGEMENT_URL=http://localhost:8005
LEADERBOARD_URL=http://localhost:8006
ANALYTICS_URL=http://localhost:8007
ADMIN_DASHBOARD_URL=http://localhost:8008

# API Configuration
API_V1_PREFIX=/api/v1
API_TITLE=Gaming Platform API
API_DESCRIPTION=Scalable Online Gaming Platform with Microservices Architecture
API_VERSION=1.0.0
API_DOCS_URL=/docs
API_REDOC_URL=/redoc

# CORS Configuration
CORS_ORIGINS=["http://localhost:3000", "http://localhost:8080", "https://yourdomain.com"]
CORS_ALLOW_CREDENTIALS=true
CORS_ALLOW_METHODS=["GET", "POST", "PUT", "DELETE", "OPTIONS", "PATCH"]
CORS_ALLOW_HEADERS=["*"]

# Rate Limiting
RATE_LIMIT_REQUESTS_PER_MINUTE=60
RATE_LIMIT_BURST=10
RATE_LIMIT_REDIS_KEY_PREFIX=rate_limit

# Security Configuration
BCRYPT_ROUNDS=12
PASSWORD_MIN_LENGTH=8
PASSWORD_REQUIRE_UPPERCASE=true
PASSWORD_REQUIRE_LOWERCASE=true
PASSWORD_REQUIRE_NUMBERS=true
PASSWORD_REQUIRE_SPECIAL_CHARS=true
SESSION_TIMEOUT_MINUTES=30
MAX_LOGIN_ATTEMPTS=5
LOCKOUT_DURATION_MINUTES=15

# Email Configuration
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your-app-password
SMTP_TLS=true
SMTP_SSL=false
EMAIL_FROM=<EMAIL>
EMAIL_FROM_NAME=Gaming Platform

# File Upload Configuration
MAX_FILE_SIZE_MB=10
ALLOWED_FILE_TYPES=["jpg", "jpeg", "png", "gif"]
UPLOAD_PATH=uploads/
AVATAR_MAX_SIZE_MB=5

# Game Configuration
DEFAULT_GAME_TIMEOUT_MINUTES=30
MAX_CONCURRENT_GAMES_PER_USER=3
ELO_K_FACTOR=32
ELO_DEFAULT_RATING=1200
MATCHMAKING_TIMEOUT_SECONDS=60
MATCHMAKING_RATING_RANGE=200

# Chat Configuration
MAX_MESSAGE_LENGTH=500
CHAT_HISTORY_LIMIT=100
PROFANITY_FILTER_ENABLED=true
CHAT_RATE_LIMIT_MESSAGES_PER_MINUTE=30

# Leaderboard Configuration
LEADERBOARD_TOP_PLAYERS=100
LEADERBOARD_UPDATE_INTERVAL_MINUTES=5
REGIONAL_LEADERBOARDS_ENABLED=true

# Analytics Configuration
ANALYTICS_BATCH_SIZE=1000
ANALYTICS_FLUSH_INTERVAL_SECONDS=60
ANALYTICS_RETENTION_DAYS=365
ENABLE_USER_TRACKING=true
ENABLE_GAME_ANALYTICS=true

# Monitoring Configuration
PROMETHEUS_ENABLED=true
PROMETHEUS_PORT=8090
METRICS_ENDPOINT=/metrics
HEALTH_CHECK_ENDPOINT=/health
LOG_LEVEL=INFO
LOG_FORMAT=json

# Development Configuration
DEBUG=false
TESTING=false
DEVELOPMENT=false
RELOAD=false

# Production Configuration
ENVIRONMENT=production
SECRET_KEY=your-super-secret-application-key-change-in-production
ALLOWED_HOSTS=["yourdomain.com", "www.yourdomain.com"]
SECURE_SSL_REDIRECT=true
SECURE_PROXY_SSL_HEADER=true

# Kubernetes Configuration
KUBERNETES_NAMESPACE=gaming-platform
KUBERNETES_SERVICE_ACCOUNT=gaming-platform-sa
KUBERNETES_CLUSTER_NAME=gaming-platform-cluster

# AWS Configuration (if using AWS)
AWS_ACCESS_KEY_ID=your-aws-access-key
AWS_SECRET_ACCESS_KEY=your-aws-secret-key
AWS_REGION=us-west-2
AWS_S3_BUCKET=gaming-platform-assets
AWS_CLOUDFRONT_DOMAIN=your-cloudfront-domain

# Google Cloud Configuration (if using GCP)
GOOGLE_CLOUD_PROJECT=your-gcp-project-id
GOOGLE_CLOUD_REGION=us-central1
GOOGLE_CLOUD_STORAGE_BUCKET=gaming-platform-assets

# Azure Configuration (if using Azure)
AZURE_STORAGE_ACCOUNT=yourstorageaccount
AZURE_STORAGE_KEY=your-storage-key
AZURE_CONTAINER_NAME=gaming-platform-assets

# Backup Configuration
BACKUP_ENABLED=true
BACKUP_SCHEDULE=0 2 * * *
BACKUP_RETENTION_DAYS=30
BACKUP_STORAGE_TYPE=s3

# Feature Flags
FEATURE_SOCIAL_LOGIN=true
FEATURE_FRIEND_SYSTEM=true
FEATURE_CHAT_SYSTEM=true
FEATURE_TOURNAMENTS=false
FEATURE_SPECTATOR_MODE=true
FEATURE_REPLAY_SYSTEM=true

# Performance Configuration
WORKER_PROCESSES=4
WORKER_CONNECTIONS=1000
KEEPALIVE_TIMEOUT=65
CLIENT_MAX_BODY_SIZE=10M
PROXY_READ_TIMEOUT=60
PROXY_CONNECT_TIMEOUT=60

# Cache Configuration
CACHE_TTL_SECONDS=300
CACHE_MAX_SIZE=1000
CACHE_ENABLED=true
CACHE_PREFIX=gaming_platform

# Logging Configuration
LOG_FILE_PATH=logs/gaming-platform.log
LOG_MAX_SIZE_MB=100
LOG_BACKUP_COUNT=5
LOG_ROTATION=daily
STRUCTURED_LOGGING=true

# Notification Configuration
PUSH_NOTIFICATIONS_ENABLED=true
EMAIL_NOTIFICATIONS_ENABLED=true
SMS_NOTIFICATIONS_ENABLED=false
NOTIFICATION_QUEUE_SIZE=10000

# Compliance Configuration
GDPR_ENABLED=true
DATA_RETENTION_DAYS=2555  # 7 years
AUDIT_LOG_RETENTION_DAYS=2555
PRIVACY_POLICY_URL=https://yourdomain.com/privacy
TERMS_OF_SERVICE_URL=https://yourdomain.com/terms

# Localization Configuration
DEFAULT_LANGUAGE=en
SUPPORTED_LANGUAGES=["en", "es", "fr", "de", "ja", "ko", "zh"]
TIMEZONE=UTC

# Testing Configuration
TEST_DATABASE_URL=postgresql://gaming_user:gaming_password@localhost:5432/gaming_platform_test
TEST_REDIS_URL=redis://localhost:6379/1
PYTEST_TIMEOUT=30
COVERAGE_THRESHOLD=80
