version: '3.8'

services:
  # Database
  postgres:
    image: postgres:15-alpine
    container_name: gaming_postgres
    environment:
      POSTGRES_DB: gaming_platform
      POSTGRES_USER: gaming_user
      POSTGRES_PASSWORD: gaming_password
      POSTGRES_HOST_AUTH_METHOD: trust
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./infrastructure/database/init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - gaming_network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U gaming_user -d gaming_platform"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: gaming_redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - gaming_network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Message Queue
  rabbitmq:
    image: rabbitmq:3.11-management-alpine
    container_name: gaming_rabbitmq
    environment:
      RABBITMQ_DEFAULT_USER: gaming_user
      RABBITMQ_DEFAULT_PASS: gaming_password
    ports:
      - "5672:5672"
      - "15672:15672"
    volumes:
      - rabbitmq_data:/var/lib/rabbitmq
    networks:
      - gaming_network
    healthcheck:
      test: ["CMD", "rabbitmq-diagnostics", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Authentication Service
  auth_service:
    build:
      context: .
      dockerfile: services/auth/Dockerfile
    container_name: gaming_auth
    environment:
      - DATABASE_URL=******************************************************/gaming_platform
      - REDIS_URL=redis://redis:6379
      - RABBITMQ_URL=amqp://gaming_user:gaming_password@rabbitmq:5672/
      - JWT_SECRET_KEY=your-super-secret-jwt-key-change-in-production
      - JWT_ALGORITHM=HS256
      - JWT_ACCESS_TOKEN_EXPIRE_MINUTES=30
      - JWT_REFRESH_TOKEN_EXPIRE_DAYS=7
    ports:
      - "8001:8000"
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
      rabbitmq:
        condition: service_healthy
    networks:
      - gaming_network
    restart: unless-stopped

  # Game Engine Service
  game_engine:
    build:
      context: .
      dockerfile: services/game-engine/Dockerfile
    container_name: gaming_engine
    environment:
      - DATABASE_URL=******************************************************/gaming_platform
      - REDIS_URL=redis://redis:6379
      - RABBITMQ_URL=amqp://gaming_user:gaming_password@rabbitmq:5672/
      - AUTH_SERVICE_URL=http://auth_service:8000
    ports:
      - "8002:8000"
    depends_on:
      - postgres
      - redis
      - rabbitmq
      - auth_service
    networks:
      - gaming_network
    restart: unless-stopped

  # Matchmaking Service
  matchmaking:
    build:
      context: .
      dockerfile: services/matchmaking/Dockerfile
    container_name: gaming_matchmaking
    environment:
      - DATABASE_URL=******************************************************/gaming_platform
      - REDIS_URL=redis://redis:6379
      - RABBITMQ_URL=amqp://gaming_user:gaming_password@rabbitmq:5672/
      - AUTH_SERVICE_URL=http://auth_service:8000
      - GAME_ENGINE_URL=http://game_engine:8000
    ports:
      - "8003:8000"
    depends_on:
      - postgres
      - redis
      - rabbitmq
      - auth_service
      - game_engine
    networks:
      - gaming_network
    restart: unless-stopped

  # Chat Service
  chat_service:
    build:
      context: .
      dockerfile: services/chat/Dockerfile
    container_name: gaming_chat
    environment:
      - DATABASE_URL=******************************************************/gaming_platform
      - REDIS_URL=redis://redis:6379
      - RABBITMQ_URL=amqp://gaming_user:gaming_password@rabbitmq:5672/
      - AUTH_SERVICE_URL=http://auth_service:8000
    ports:
      - "8004:8000"
    depends_on:
      - postgres
      - redis
      - rabbitmq
      - auth_service
    networks:
      - gaming_network
    restart: unless-stopped

  # User Management Service
  user_management:
    build:
      context: .
      dockerfile: services/user-management/Dockerfile
    container_name: gaming_users
    environment:
      - DATABASE_URL=******************************************************/gaming_platform
      - REDIS_URL=redis://redis:6379
      - RABBITMQ_URL=amqp://gaming_user:gaming_password@rabbitmq:5672/
      - AUTH_SERVICE_URL=http://auth_service:8000
    ports:
      - "8005:8000"
    depends_on:
      - postgres
      - redis
      - rabbitmq
      - auth_service
    networks:
      - gaming_network
    restart: unless-stopped

  # Leaderboard Service
  leaderboard:
    build:
      context: .
      dockerfile: services/leaderboard/Dockerfile
    container_name: gaming_leaderboard
    environment:
      - DATABASE_URL=******************************************************/gaming_platform
      - REDIS_URL=redis://redis:6379
      - RABBITMQ_URL=amqp://gaming_user:gaming_password@rabbitmq:5672/
      - AUTH_SERVICE_URL=http://auth_service:8000
    ports:
      - "8006:8000"
    depends_on:
      - postgres
      - redis
      - rabbitmq
      - auth_service
    networks:
      - gaming_network
    restart: unless-stopped

  # Analytics Service
  analytics:
    build:
      context: .
      dockerfile: services/analytics/Dockerfile
    container_name: gaming_analytics
    environment:
      - DATABASE_URL=******************************************************/gaming_platform
      - REDIS_URL=redis://redis:6379
      - RABBITMQ_URL=amqp://gaming_user:gaming_password@rabbitmq:5672/
      - AUTH_SERVICE_URL=http://auth_service:8000
    ports:
      - "8007:8000"
    depends_on:
      - postgres
      - redis
      - rabbitmq
      - auth_service
    networks:
      - gaming_network
    restart: unless-stopped

  # Admin Dashboard
  admin_dashboard:
    build:
      context: .
      dockerfile: services/admin/Dockerfile
    container_name: gaming_admin
    environment:
      - DATABASE_URL=******************************************************/gaming_platform
      - REDIS_URL=redis://redis:6379
      - AUTH_SERVICE_URL=http://auth_service:8000
      - GAME_ENGINE_URL=http://game_engine:8000
      - USER_MANAGEMENT_URL=http://user_management:8000
      - ANALYTICS_URL=http://analytics:8000
    ports:
      - "8008:8000"
    depends_on:
      - postgres
      - redis
      - auth_service
    networks:
      - gaming_network
    restart: unless-stopped

  # Monitoring - Prometheus
  prometheus:
    image: prom/prometheus:latest
    container_name: gaming_prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./infrastructure/monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    networks:
      - gaming_network

  # Monitoring - Grafana
  grafana:
    image: grafana/grafana:latest
    container_name: gaming_grafana
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
    volumes:
      - grafana_data:/var/lib/grafana
      - ./infrastructure/monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ./infrastructure/monitoring/grafana/datasources:/etc/grafana/provisioning/datasources
    networks:
      - gaming_network

  # API Gateway (Nginx)
  nginx:
    image: nginx:alpine
    container_name: gaming_gateway
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./infrastructure/nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./infrastructure/nginx/ssl:/etc/nginx/ssl
    depends_on:
      - auth_service
      - game_engine
      - matchmaking
      - chat_service
      - user_management
      - leaderboard
      - analytics
      - admin_dashboard
    networks:
      - gaming_network
    restart: unless-stopped

volumes:
  postgres_data:
  redis_data:
  rabbitmq_data:
  prometheus_data:
  grafana_data:

networks:
  gaming_network:
    driver: bridge
