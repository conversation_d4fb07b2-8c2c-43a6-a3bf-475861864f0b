#!/usr/bin/env python3
"""
Demonstration script showing that MindLink now creates files in folder 3.
"""

import sys
import os
from pathlib import Path

# Add the project root to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from mindlink.tools.file_tools import create_file

def demonstrate_folder_3_creation():
    """Demonstrate that files are created in folder 3."""
    print("🎯 Demonstrating File Creation in Folder 3")
    print("=" * 50)
    
    # Show current configuration
    from mindlink.tools.file_tools import SAFE_BASE_DIR
    print(f"📁 SAFE_BASE_DIR: {SAFE_BASE_DIR}")
    print(f"📁 Absolute path: {SAFE_BASE_DIR.absolute()}")
    
    # Create a demo file
    demo_filename = "demo_file.py"
    demo_content = '''"""
Demo file created in folder 3 by MindLink system.
This demonstrates that the file creation path has been successfully configured.
"""

def main():
    print("Hello from folder 3!")
    print("This file was created by the MindLink system.")
    print(f"File location: {__file__}")

if __name__ == "__main__":
    main()
'''
    
    print(f"\n🔧 Creating demo file: {demo_filename}")
    
    try:
        # Create the file using MindLink's file tools
        create_file(demo_filename, demo_content)
        
        # Check where it was created
        expected_path = Path("3") / demo_filename
        
        if expected_path.exists():
            print(f"✅ File created successfully!")
            print(f"📍 Location: {expected_path.absolute()}")
            
            # Show file content preview
            content = expected_path.read_text()
            print(f"\n📄 File content preview:")
            print("─" * 40)
            print(content[:200] + "..." if len(content) > 200 else content)
            print("─" * 40)
            
            # Run the demo file to show it works
            print(f"\n🚀 Running the demo file:")
            try:
                import subprocess
                result = subprocess.run([sys.executable, str(expected_path)], 
                                      capture_output=True, text=True, cwd=".")
                if result.returncode == 0:
                    print("Output:")
                    print(result.stdout)
                else:
                    print(f"Error running file: {result.stderr}")
            except Exception as e:
                print(f"Could not run demo file: {e}")
            
            return True
        else:
            print(f"❌ File not found at expected location: {expected_path.absolute()}")
            return False
            
    except Exception as e:
        print(f"❌ Failed to create demo file: {e}")
        return False

def show_folder_structure():
    """Show the current folder structure."""
    print(f"\n📂 Current Folder Structure:")
    print("─" * 30)
    
    workspace = Path(".")
    folder_3 = workspace / "3"
    
    print(f"Workspace: {workspace.absolute()}")
    print(f"├── 3/ (Project files directory)")
    
    if folder_3.exists():
        files_in_3 = list(folder_3.iterdir())
        if files_in_3:
            for i, item in enumerate(files_in_3):
                is_last = i == len(files_in_3) - 1
                prefix = "└──" if is_last else "├──"
                print(f"│   {prefix} {item.name}")
        else:
            print(f"│   └── (empty)")
    else:
        print(f"│   └── (does not exist)")
    
    print(f"├── mindlink/ (Core system)")
    print(f"├── tests/ (Test files)")
    print(f"└── ... (other project files)")

def main():
    """Main demonstration function."""
    print("🎉 MindLink Folder 3 Configuration Complete!")
    print("\nYour MindLink system has been successfully configured to create")
    print("all project files in the dedicated '3' folder within your workspace.")
    print("\nThis keeps your core project organized and separates generated")
    print("files from the MindLink system files.")
    
    # Run demonstration
    success = demonstrate_folder_3_creation()
    
    # Show folder structure
    show_folder_structure()
    
    print("\n" + "=" * 50)
    if success:
        print("✅ Configuration verified successfully!")
        print("\n📋 Summary:")
        print("• Folder 3 created: ✅")
        print("• SAFE_BASE_DIR configured: ✅") 
        print("• File creation tested: ✅")
        print("• Path normalization working: ✅")
        print("\n🎯 Next Steps:")
        print("• Your gaming platform project will now create all 40 files in folder 3")
        print("• All future MindLink projects will be organized in this dedicated space")
        print("• Your main workspace stays clean and organized")
        print(f"\n📁 Project files location: {Path('3').absolute()}")
    else:
        print("❌ Configuration verification failed!")
        print("Please check the setup and try again.")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
