#!/usr/bin/env python3
"""
Test script to verify MindLink's capacity for large projects (up to 100 files).
This script tests the enhanced configuration and capabilities.
"""

import sys
import os
import tempfile
import shutil
from pathlib import Path

# Add the project root to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from mindlink import run_agent
from mindlink.config import load_config

def test_large_project_configuration():
    """Test that the configuration supports large projects."""
    print("🔧 Testing Large Project Configuration...")
    
    config = load_config()
    
    # Check max_steps
    max_steps = config["agent"]["max_steps"]
    print(f"   Max steps: {max_steps}")
    assert max_steps >= 120, f"Max steps should be at least 120, got {max_steps}"
    
    # Check analysis tokens
    analysis_tokens = config["intelligent_analysis"]["analysis_max_tokens"]
    print(f"   Analysis max tokens: {analysis_tokens}")
    assert analysis_tokens >= 8192, f"Analysis tokens should be at least 8192, got {analysis_tokens}"
    
    # Check large project settings
    max_files = config["intelligent_analysis"]["max_project_files"]
    print(f"   Max project files: {max_files}")
    assert max_files >= 100, f"Max project files should be at least 100, got {max_files}"
    
    print("✅ Configuration test passed!")
    return True

def test_complexity_analysis():
    """Test the enhanced complexity analysis for large projects."""
    print("\n🧠 Testing Enhanced Complexity Analysis...")
    
    from mindlink.agent import AgentOS
    from mindlink.main import create_llm
    
    config = load_config()
    llm = create_llm(config)
    agent = AgentOS(
        llm=llm,
        system_prompt_template=config["agent"]["system_prompt_template"],
        max_steps=config["agent"]["max_steps"]
    )
    
    # Test different complexity levels
    test_cases = [
        ("Create a simple file", 5),
        ("Build a web application with database", 15),
        ("Create a scalable gaming platform with microservices", 60),
        ("Build an enterprise platform with 50 files", 60),
        ("Create 100 files for a comprehensive system", 120)
    ]
    
    for goal, expected_min_steps in test_cases:
        complexity = agent._analyze_goal_complexity(goal)
        print(f"   '{goal}' -> {complexity} steps (expected >= {expected_min_steps})")
        assert complexity >= expected_min_steps, f"Complexity analysis failed for: {goal}"
    
    print("✅ Complexity analysis test passed!")
    return True

def test_file_path_handling():
    """Test that file paths are handled correctly (no hardcoded D:/3/)."""
    print("\n📁 Testing File Path Handling...")
    
    from mindlink.agent import AgentOS
    from mindlink.main import create_llm
    
    config = load_config()
    llm = create_llm(config)
    agent = AgentOS(
        llm=llm,
        system_prompt_template=config["agent"]["system_prompt_template"],
        max_steps=config["agent"]["max_steps"]
    )
    
    # Create a mock multi-file context
    ctx = {
        'file_description_base': 'test project',
        'original_goal': 'Create a test project',
        'llm_analysis': {
            'file_structure': [
                {'filename': 'main.py', 'purpose': 'Main entry point'}
            ]
        }
    }
    
    filename = agent._get_intelligent_filename(ctx, 1)
    print(f"   Generated filename: {filename}")
    
    # Should not contain hardcoded D:/3/ path
    assert not filename.startswith('D:/3/'), f"Filename should not contain hardcoded path: {filename}"
    
    print("✅ File path handling test passed!")
    return True

def test_small_project_creation():
    """Test creating a small project to verify the system works."""
    print("\n🚀 Testing Small Project Creation...")
    
    # Create a temporary directory for testing
    with tempfile.TemporaryDirectory() as temp_dir:
        os.chdir(temp_dir)
        
        goal = "Create a simple Python project with 3 files: main.py, config.py, and utils.py"
        
        try:
            success, result, history = run_agent(goal, max_steps=20)
            print(f"   Success: {success}")
            print(f"   Result: {result[:100]}..." if len(result) > 100 else f"   Result: {result}")
            
            # Check if files were created
            created_files = list(Path('.').glob('*.py'))
            print(f"   Created files: {[f.name for f in created_files]}")
            
            if success and len(created_files) > 0:
                print("✅ Small project creation test passed!")
                return True
            else:
                print("⚠️  Small project creation test completed with issues")
                return False
                
        except Exception as e:
            print(f"❌ Small project creation test failed: {e}")
            return False

def test_large_project_planning():
    """Test planning for a large project without execution."""
    print("\n📋 Testing Large Project Planning...")
    
    from mindlink.agent import AgentOS
    from mindlink.main import create_llm
    
    config = load_config()
    llm = create_llm(config)
    agent = AgentOS(
        llm=llm,
        system_prompt_template=config["agent"]["system_prompt_template"],
        max_steps=config["agent"]["max_steps"]
    )
    
    # Test planning for a large project
    goal = """Create a scalable web application with the following components:
    - Frontend (React with 10 components)
    - Backend API (FastAPI with 15 endpoints)
    - Database models (5 models)
    - Authentication system
    - Configuration files
    - Docker setup
    - Testing files
    - Documentation
    Total: approximately 40 files"""
    
    try:
        plan = agent.plan_once(goal)
        print(f"   Generated plan with {len(plan)} actions")
        
        # Count file creation actions
        file_actions = [action for action in plan if action.tool_name in ['create_file', 'generate_large_file']]
        print(f"   File creation actions: {len(file_actions)}")
        
        if len(plan) > 20 and len(file_actions) > 10:
            print("✅ Large project planning test passed!")
            return True
        else:
            print("⚠️  Large project planning test completed with limited scope")
            return False
            
    except Exception as e:
        print(f"❌ Large project planning test failed: {e}")
        return False

def main():
    """Run all tests to verify large project capacity."""
    print("🧪 Testing MindLink Large Project Capacity (up to 100 files)")
    print("=" * 60)
    
    tests = [
        test_large_project_configuration,
        test_complexity_analysis,
        test_file_path_handling,
        test_small_project_creation,
        test_large_project_planning
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"❌ Test failed with exception: {e}")
    
    print("\n" + "=" * 60)
    print(f"🏁 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! MindLink is ready for large projects.")
        return True
    else:
        print("⚠️  Some tests failed. Review the configuration and implementation.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
