#!/usr/bin/env python3
"""
Test script to verify where files are created by the MindLink system.
"""

import sys
import os
import tempfile
import shutil
from pathlib import Path

# Add the project root to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from mindlink import run_agent

def test_file_creation_location():
    """Test where files are actually created."""
    print("🧪 Testing File Creation Location...")
    
    # Get current working directory
    original_cwd = os.getcwd()
    print(f"   Original working directory: {original_cwd}")
    
    # Create a temporary directory for testing
    with tempfile.TemporaryDirectory() as temp_dir:
        print(f"   Test directory: {temp_dir}")
        
        # Change to test directory
        os.chdir(temp_dir)
        print(f"   Changed to: {os.getcwd()}")
        
        # Test simple file creation
        goal = "Create a simple Python file called test_location.py with a hello world function"
        
        try:
            success, result, history = run_agent(goal, max_steps=10)
            print(f"   Success: {success}")
            print(f"   Result: {result[:100]}..." if len(result) > 100 else f"   Result: {result}")
            
            # Check where files were created
            print("\n   Files in current directory:")
            current_files = list(Path('.').glob('*'))
            for f in current_files:
                print(f"     {f} ({'file' if f.is_file() else 'dir'})")
            
            # Check if test_location.py was created
            test_file = Path('test_location.py')
            if test_file.exists():
                print(f"   ✅ File created successfully: {test_file.absolute()}")
                print(f"   File content preview:")
                content = test_file.read_text()[:200]
                print(f"     {content}...")
                return True
            else:
                print(f"   ❌ File not found in expected location: {test_file.absolute()}")
                
                # Check if it was created elsewhere
                print("   Searching for files in parent directories...")
                for parent in [Path('..'), Path('../..'), Path('../../..')]:
                    if parent.exists():
                        found_files = list(parent.glob('**/test_location.py'))
                        if found_files:
                            print(f"     Found file at: {found_files[0].absolute()}")
                            return False
                
                return False
                
        except Exception as e:
            print(f"   ❌ Test failed with exception: {e}")
            return False
        finally:
            # Restore original working directory
            os.chdir(original_cwd)

def test_safe_base_dir():
    """Test the SAFE_BASE_DIR configuration."""
    print("\n🔧 Testing SAFE_BASE_DIR Configuration...")
    
    from mindlink.tools.file_tools import SAFE_BASE_DIR
    print(f"   SAFE_BASE_DIR: {SAFE_BASE_DIR}")
    print(f"   SAFE_BASE_DIR absolute: {SAFE_BASE_DIR.absolute()}")
    print(f"   Current working directory: {os.getcwd()}")
    
    # Check if they match
    if str(SAFE_BASE_DIR.absolute()) == str(Path(os.getcwd()).absolute()):
        print("   ✅ SAFE_BASE_DIR matches current working directory")
        return True
    else:
        print("   ❌ SAFE_BASE_DIR does not match current working directory")
        return False

def test_file_tools_directly():
    """Test file creation using file tools directly."""
    print("\n🛠️  Testing File Tools Directly...")
    
    from mindlink.tools.file_tools import create_file, path_exists
    
    test_filename = "direct_test.py"
    test_content = "# Direct test file\nprint('Hello from direct test')\n"
    
    try:
        # Create file directly
        create_file(test_filename, test_content)
        
        # Check if it exists
        if path_exists(test_filename):
            print(f"   ✅ File created successfully: {Path(test_filename).absolute()}")
            
            # Clean up
            try:
                os.remove(test_filename)
                print("   🧹 Cleaned up test file")
            except:
                pass
            
            return True
        else:
            print(f"   ❌ File not found after creation")
            return False
            
    except Exception as e:
        print(f"   ❌ Direct file creation failed: {e}")
        return False

def main():
    """Run all file location tests."""
    print("📍 Testing MindLink File Creation Location")
    print("=" * 50)
    
    tests = [
        test_safe_base_dir,
        test_file_tools_directly,
        test_file_creation_location
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"❌ Test failed with exception: {e}")
    
    print("\n" + "=" * 50)
    print(f"🏁 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Files are created in the current workspace.")
        print(f"📁 Files will be created in: {os.getcwd()}")
    else:
        print("⚠️  Some tests failed. Check the file creation configuration.")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
