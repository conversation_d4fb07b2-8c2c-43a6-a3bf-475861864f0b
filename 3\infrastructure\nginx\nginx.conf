# Gaming Platform - Nginx API Gateway Configuration
# Handles load balancing, SSL termination, and request routing

user nginx;
worker_processes auto;
error_log /var/log/nginx/error.log warn;
pid /var/run/nginx.pid;

# Load dynamic modules
load_module modules/ngx_http_geoip_module.so;

events {
    worker_connections 1024;
    use epoll;
    multi_accept on;
}

http {
    # Basic Settings
    include /etc/nginx/mime.types;
    default_type application/octet-stream;
    
    # Logging Format
    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                    '$status $body_bytes_sent "$http_referer" '
                    '"$http_user_agent" "$http_x_forwarded_for" '
                    'rt=$request_time uct="$upstream_connect_time" '
                    'uht="$upstream_header_time" urt="$upstream_response_time"';
    
    access_log /var/log/nginx/access.log main;
    
    # Performance Settings
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    types_hash_max_size 2048;
    client_max_body_size 10M;
    
    # Gzip Compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied any;
    gzip_comp_level 6;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/json
        application/javascript
        application/xml+rss
        application/atom+xml
        image/svg+xml;
    
    # Rate Limiting
    limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
    limit_req_zone $binary_remote_addr zone=auth:10m rate=5r/s;
    limit_req_zone $binary_remote_addr zone=websocket:10m rate=20r/s;
    
    # Connection Limiting
    limit_conn_zone $binary_remote_addr zone=perip:10m;
    limit_conn_zone $server_name zone=perserver:10m;
    
    # Security Headers
    add_header X-Frame-Options DENY always;
    add_header X-Content-Type-Options nosniff always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self'; connect-src 'self' wss: ws:;" always;
    
    # Hide Nginx version
    server_tokens off;
    
    # Upstream Services
    upstream auth_service {
        least_conn;
        server auth_service:8000 max_fails=3 fail_timeout=30s;
        keepalive 32;
    }
    
    upstream game_engine {
        least_conn;
        server game_engine:8000 max_fails=3 fail_timeout=30s;
        keepalive 32;
    }
    
    upstream matchmaking {
        least_conn;
        server matchmaking:8000 max_fails=3 fail_timeout=30s;
        keepalive 32;
    }
    
    upstream chat_service {
        least_conn;
        server chat_service:8000 max_fails=3 fail_timeout=30s;
        keepalive 32;
    }
    
    upstream user_management {
        least_conn;
        server user_management:8000 max_fails=3 fail_timeout=30s;
        keepalive 32;
    }
    
    upstream leaderboard {
        least_conn;
        server leaderboard:8000 max_fails=3 fail_timeout=30s;
        keepalive 32;
    }
    
    upstream analytics {
        least_conn;
        server analytics:8000 max_fails=3 fail_timeout=30s;
        keepalive 32;
    }
    
    upstream admin_dashboard {
        least_conn;
        server admin_dashboard:8000 max_fails=3 fail_timeout=30s;
        keepalive 32;
    }
    
    # Health Check
    server {
        listen 8080;
        server_name _;
        
        location /health {
            access_log off;
            return 200 "healthy\n";
            add_header Content-Type text/plain;
        }
        
        location /nginx_status {
            stub_status on;
            access_log off;
            allow 127.0.0.1;
            allow 10.0.0.0/8;
            allow **********/12;
            allow ***********/16;
            deny all;
        }
    }
    
    # HTTP to HTTPS Redirect
    server {
        listen 80;
        server_name _;
        return 301 https://$host$request_uri;
    }
    
    # Main HTTPS Server
    server {
        listen 443 ssl http2;
        server_name gaming-platform.com www.gaming-platform.com;
        
        # SSL Configuration
        ssl_certificate /etc/nginx/ssl/cert.pem;
        ssl_certificate_key /etc/nginx/ssl/key.pem;
        ssl_protocols TLSv1.2 TLSv1.3;
        ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
        ssl_prefer_server_ciphers off;
        ssl_session_cache shared:SSL:10m;
        ssl_session_timeout 10m;
        ssl_stapling on;
        ssl_stapling_verify on;
        
        # Connection and Rate Limiting
        limit_conn perip 20;
        limit_conn perserver 1000;
        
        # Common proxy settings
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Forwarded-Host $host;
        proxy_set_header X-Forwarded-Port $server_port;
        
        # Timeouts
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
        
        # Buffering
        proxy_buffering on;
        proxy_buffer_size 4k;
        proxy_buffers 8 4k;
        proxy_busy_buffers_size 8k;
        
        # Authentication Service
        location /api/v1/auth {
            limit_req zone=auth burst=10 nodelay;
            proxy_pass http://auth_service;
            proxy_http_version 1.1;
            proxy_set_header Connection "";
        }
        
        # Game Engine Service
        location /api/v1/games {
            limit_req zone=api burst=20 nodelay;
            proxy_pass http://game_engine;
            proxy_http_version 1.1;
            proxy_set_header Connection "";
        }
        
        location /api/v1/moves {
            limit_req zone=api burst=30 nodelay;
            proxy_pass http://game_engine;
            proxy_http_version 1.1;
            proxy_set_header Connection "";
        }
        
        location /api/v1/rules {
            limit_req zone=api burst=10 nodelay;
            proxy_pass http://game_engine;
            proxy_http_version 1.1;
            proxy_set_header Connection "";
        }
        
        # WebSocket for real-time gameplay
        location /ws/game {
            limit_req zone=websocket burst=5 nodelay;
            proxy_pass http://game_engine;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "upgrade";
            proxy_set_header Host $host;
            proxy_cache_bypass $http_upgrade;
            proxy_read_timeout 86400;
        }
        
        # Matchmaking Service
        location /api/v1/matchmaking {
            limit_req zone=api burst=15 nodelay;
            proxy_pass http://matchmaking;
            proxy_http_version 1.1;
            proxy_set_header Connection "";
        }
        
        location /api/v1/queue {
            limit_req zone=api burst=15 nodelay;
            proxy_pass http://matchmaking;
            proxy_http_version 1.1;
            proxy_set_header Connection "";
        }
        
        location /api/v1/elo {
            limit_req zone=api burst=10 nodelay;
            proxy_pass http://matchmaking;
            proxy_http_version 1.1;
            proxy_set_header Connection "";
        }
        
        # Chat Service
        location /api/v1/chat {
            limit_req zone=api burst=25 nodelay;
            proxy_pass http://chat_service;
            proxy_http_version 1.1;
            proxy_set_header Connection "";
        }
        
        # WebSocket for real-time chat
        location /ws/chat {
            limit_req zone=websocket burst=10 nodelay;
            proxy_pass http://chat_service;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "upgrade";
            proxy_set_header Host $host;
            proxy_cache_bypass $http_upgrade;
            proxy_read_timeout 86400;
        }
        
        # User Management Service
        location /api/v1/users {
            limit_req zone=api burst=15 nodelay;
            proxy_pass http://user_management;
            proxy_http_version 1.1;
            proxy_set_header Connection "";
        }
        
        location /api/v1/friends {
            limit_req zone=api burst=10 nodelay;
            proxy_pass http://user_management;
            proxy_http_version 1.1;
            proxy_set_header Connection "";
        }
        
        # Leaderboard Service
        location /api/v1/leaderboard {
            limit_req zone=api burst=10 nodelay;
            proxy_pass http://leaderboard;
            proxy_http_version 1.1;
            proxy_set_header Connection "";
            
            # Cache leaderboard responses
            proxy_cache_valid 200 5m;
            proxy_cache_use_stale error timeout updating http_500 http_502 http_503 http_504;
        }
        
        # Analytics Service
        location /api/v1/analytics {
            limit_req zone=api burst=5 nodelay;
            proxy_pass http://analytics;
            proxy_http_version 1.1;
            proxy_set_header Connection "";
        }
        
        # Admin Dashboard
        location /admin {
            limit_req zone=api burst=5 nodelay;
            proxy_pass http://admin_dashboard;
            proxy_http_version 1.1;
            proxy_set_header Connection "";
            
            # Additional security for admin
            allow 10.0.0.0/8;
            allow **********/12;
            allow ***********/16;
            deny all;
        }
        
        # Health checks
        location /health {
            access_log off;
            return 200 "healthy\n";
            add_header Content-Type text/plain;
        }
        
        # Static files (if any)
        location /static {
            alias /var/www/static;
            expires 1y;
            add_header Cache-Control "public, immutable";
        }
        
        # Default location
        location / {
            return 404;
        }
    }
}
