[tool:pytest]
# Pytest configuration for the Gaming Platform

# Test discovery
testpaths = tests
python_files = test_*.py *_test.py
python_classes = Test*
python_functions = test_*

# Minimum version
minversion = 7.0

# Add options
addopts = 
    --strict-markers
    --strict-config
    --verbose
    --tb=short
    --cov=services
    --cov=shared
    --cov-report=term-missing
    --cov-report=html:htmlcov
    --cov-report=xml:coverage.xml
    --cov-fail-under=80
    --durations=10
    --maxfail=5
    -ra

# Markers
markers =
    unit: Unit tests
    integration: Integration tests
    e2e: End-to-end tests
    slow: Slow running tests
    auth: Authentication related tests
    game: Game engine related tests
    matchmaking: Matchmaking related tests
    chat: Chat service related tests
    user: User management related tests
    leaderboard: Leaderboard related tests
    analytics: Analytics related tests
    admin: Admin dashboard related tests
    database: Database related tests
    redis: Redis related tests
    rabbitmq: RabbitMQ related tests
    websocket: WebSocket related tests
    api: API endpoint tests
    security: Security related tests
    performance: Performance tests
    smoke: Smoke tests
    regression: Regression tests

# Test timeout
timeout = 300

# Asyncio mode
asyncio_mode = auto

# Warnings
filterwarnings =
    error
    ignore::UserWarning
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning
    ignore:.*unclosed.*:ResourceWarning

# Environment variables for testing
env =
    ENVIRONMENT = testing
    DEBUG = true
    TESTING = true
    DATABASE_URL = postgresql://gaming_user:gaming_password@localhost:5432/gaming_platform_test
    REDIS_URL = redis://localhost:6379/1
    RABBITMQ_URL = amqp://gaming_user:gaming_password@localhost:5672/test
    JWT_SECRET_KEY = test-secret-key-for-testing-only-32-chars
    LOG_LEVEL = DEBUG
    RATE_LIMIT_REQUESTS_PER_MINUTE = 1000
    CACHE_ENABLED = false

# Logging
log_cli = true
log_cli_level = INFO
log_cli_format = %(asctime)s [%(levelname)8s] %(name)s: %(message)s
log_cli_date_format = %Y-%m-%d %H:%M:%S

# Coverage
[coverage:run]
source = services, shared
omit = 
    */tests/*
    */test_*
    */__pycache__/*
    */migrations/*
    */venv/*
    */env/*
    */.venv/*
    setup.py
    conftest.py

[coverage:report]
exclude_lines =
    pragma: no cover
    def __repr__
    if self.debug:
    if settings.DEBUG
    raise AssertionError
    raise NotImplementedError
    if 0:
    if __name__ == .__main__.:
    class .*\bProtocol\):
    @(abc\.)?abstractmethod

[coverage:html]
directory = htmlcov

[coverage:xml]
output = coverage.xml
