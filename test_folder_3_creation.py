#!/usr/bin/env python3
"""
Test script to verify that files are created in folder 3.
"""

import sys
import os
from pathlib import Path

# Add the project root to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_safe_base_dir_folder_3():
    """Test that SAFE_BASE_DIR points to folder 3."""
    print("🔧 Testing SAFE_BASE_DIR Configuration for Folder 3...")
    
    from mindlink.tools.file_tools import SAFE_BASE_DIR
    
    expected_path = Path(os.getcwd()) / "3"
    
    print(f"   SAFE_BASE_DIR: {SAFE_BASE_DIR}")
    print(f"   Expected path: {expected_path}")
    print(f"   SAFE_BASE_DIR absolute: {SAFE_BASE_DIR.absolute()}")
    print(f"   Expected absolute: {expected_path.absolute()}")
    
    # Check if they match
    if str(SAFE_BASE_DIR.absolute()) == str(expected_path.absolute()):
        print("   ✅ SAFE_BASE_DIR correctly points to folder 3")
        return True
    else:
        print("   ❌ SAFE_BASE_DIR does not point to folder 3")
        return False

def test_folder_3_exists():
    """Test that folder 3 exists."""
    print("\n📁 Testing Folder 3 Existence...")
    
    folder_3 = Path("3")
    
    if folder_3.exists() and folder_3.is_dir():
        print(f"   ✅ Folder 3 exists: {folder_3.absolute()}")
        return True
    else:
        print(f"   ❌ Folder 3 does not exist: {folder_3.absolute()}")
        return False

def test_direct_file_creation_in_folder_3():
    """Test creating a file directly in folder 3 using file tools."""
    print("\n🛠️  Testing Direct File Creation in Folder 3...")
    
    from mindlink.tools.file_tools import create_file, path_exists
    
    test_filename = "test_folder_3.py"
    test_content = "# Test file created in folder 3\nprint('Hello from folder 3!')\n"
    
    try:
        # Create file directly
        create_file(test_filename, test_content)
        
        # Check if it exists in folder 3
        expected_path = Path("3") / test_filename
        if expected_path.exists():
            print(f"   ✅ File created successfully in folder 3: {expected_path.absolute()}")
            
            # Verify content
            content = expected_path.read_text()
            if test_content in content:
                print("   ✅ File content is correct")
            else:
                print("   ⚠️  File content differs from expected")
            
            # Clean up
            try:
                expected_path.unlink()
                print("   🧹 Cleaned up test file")
            except:
                pass
            
            return True
        else:
            print(f"   ❌ File not found in folder 3: {expected_path.absolute()}")
            
            # Check if it was created elsewhere
            for location in [Path("."), Path("3")]:
                test_file = location / test_filename
                if test_file.exists():
                    print(f"   📍 File found at: {test_file.absolute()}")
                    try:
                        test_file.unlink()
                    except:
                        pass
            
            return False
            
    except Exception as e:
        print(f"   ❌ File creation failed: {e}")
        return False

def test_agent_file_creation_in_folder_3():
    """Test that the agent creates files in folder 3."""
    print("\n🤖 Testing Agent File Creation in Folder 3...")
    
    # This test would require LLM calls which might hit rate limits
    # So we'll just test the path configuration logic
    
    from mindlink.agent import AgentOS
    from mindlink.main import create_llm
    from mindlink.config import load_config
    
    try:
        config = load_config()
        
        # Test the path normalization logic
        print("   Testing path normalization logic...")
        
        # Simulate what happens when the agent processes a file creation
        test_path = "test_file.py"
        expected_result = "3/test_file.py"
        
        # This simulates the logic in the agent for default file paths
        result_path = f"3/{test_path}"
        
        if result_path == expected_result:
            print(f"   ✅ Path normalization works: '{test_path}' -> '{result_path}'")
            return True
        else:
            print(f"   ❌ Path normalization failed: '{test_path}' -> '{result_path}' (expected: '{expected_result}')")
            return False
            
    except Exception as e:
        print(f"   ❌ Agent configuration test failed: {e}")
        return False

def test_folder_structure():
    """Test the overall folder structure."""
    print("\n📂 Testing Folder Structure...")
    
    workspace = Path(".")
    folder_3 = workspace / "3"
    
    print(f"   Workspace: {workspace.absolute()}")
    print(f"   Folder 3: {folder_3.absolute()}")
    
    # List contents
    print("   Workspace contents:")
    for item in workspace.iterdir():
        if item.name not in ['.git', '__pycache__', '.pytest_cache']:
            print(f"     {item.name} ({'dir' if item.is_dir() else 'file'})")
    
    print("   Folder 3 contents:")
    if folder_3.exists():
        contents = list(folder_3.iterdir())
        if contents:
            for item in contents:
                print(f"     {item.name} ({'dir' if item.is_dir() else 'file'})")
        else:
            print("     (empty)")
    else:
        print("     (does not exist)")
    
    return True

def main():
    """Run all tests to verify folder 3 configuration."""
    print("📁 Testing MindLink Folder 3 File Creation")
    print("=" * 50)
    
    tests = [
        test_folder_3_exists,
        test_safe_base_dir_folder_3,
        test_direct_file_creation_in_folder_3,
        test_agent_file_creation_in_folder_3,
        test_folder_structure
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"❌ Test failed with exception: {e}")
    
    print("\n" + "=" * 50)
    print(f"🏁 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Files will be created in folder 3.")
        print(f"📁 File creation path: {Path('3').absolute()}")
    else:
        print("⚠️  Some tests failed. Check the folder 3 configuration.")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
