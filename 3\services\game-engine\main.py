"""
Game Engine Service - Main Application
Handles game logic, state management, and rule enforcement for turn-based games.
"""

import logging
import asyncio
from contextlib import asynccontextmanager
from typing import Dict, Any, List

import uvicorn
from fastapi import FastAPI, HTTPException, Depends, status, WebSocket, WebSocketDisconnect
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from prometheus_client import Counter, Histogram, Gauge, generate_latest
from starlette.responses import Response

from .config import get_settings
from .database import get_db, init_db
from .routers import games, moves, rules
from .middleware import (
    LoggingMiddleware,
    RateLimitMiddleware,
    SecurityHeadersMiddleware,
    RequestIDMiddleware
)
from .utils.health import <PERSON>Checker
from .utils.metrics import MetricsCollector
from .game_logic import GameManager
from .websocket_manager import WebSocketManager

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Metrics
REQUEST_COUNT = Counter(
    'game_engine_requests_total',
    'Total requests to game engine service',
    ['method', 'endpoint', 'status']
)

REQUEST_DURATION = Histogram(
    'game_engine_request_duration_seconds',
    'Request duration in seconds',
    ['method', 'endpoint']
)

ACTIVE_GAMES = Gauge(
    'game_engine_active_games',
    'Number of active games'
)

WEBSOCKET_CONNECTIONS = Gauge(
    'game_engine_websocket_connections',
    'Number of active WebSocket connections'
)

GAME_MOVES_TOTAL = Counter(
    'game_engine_moves_total',
    'Total number of game moves',
    ['game_type']
)


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager."""
    # Startup
    logger.info("Starting Game Engine Service...")
    
    # Initialize database
    await init_db()
    logger.info("Database initialized")
    
    # Initialize game manager
    game_manager = GameManager()
    app.state.game_manager = game_manager
    await game_manager.initialize()
    logger.info("Game manager initialized")
    
    # Initialize WebSocket manager
    websocket_manager = WebSocketManager()
    app.state.websocket_manager = websocket_manager
    logger.info("WebSocket manager initialized")
    
    # Initialize health checker
    health_checker = HealthChecker()
    app.state.health_checker = health_checker
    
    # Initialize metrics collector
    metrics_collector = MetricsCollector()
    app.state.metrics_collector = metrics_collector
    
    # Start background tasks
    asyncio.create_task(game_cleanup_task(game_manager))
    asyncio.create_task(metrics_update_task(game_manager, websocket_manager))
    
    logger.info("Game Engine Service started successfully")
    
    yield
    
    # Shutdown
    logger.info("Shutting down Game Engine Service...")
    
    # Cleanup resources
    if hasattr(app.state, 'game_manager'):
        await app.state.game_manager.cleanup()
    
    if hasattr(app.state, 'websocket_manager'):
        await app.state.websocket_manager.cleanup()
    
    if hasattr(app.state, 'health_checker'):
        await app.state.health_checker.cleanup()
    
    if hasattr(app.state, 'metrics_collector'):
        await app.state.metrics_collector.cleanup()
    
    logger.info("Game Engine Service shutdown complete")


async def game_cleanup_task(game_manager: GameManager):
    """Background task to cleanup abandoned games."""
    while True:
        try:
            await game_manager.cleanup_abandoned_games()
            await asyncio.sleep(300)  # Run every 5 minutes
        except Exception as e:
            logger.error(f"Game cleanup task error: {e}")
            await asyncio.sleep(60)  # Retry after 1 minute on error


async def metrics_update_task(game_manager: GameManager, websocket_manager: WebSocketManager):
    """Background task to update metrics."""
    while True:
        try:
            # Update active games metric
            active_games_count = await game_manager.get_active_games_count()
            ACTIVE_GAMES.set(active_games_count)
            
            # Update WebSocket connections metric
            websocket_connections_count = websocket_manager.get_connection_count()
            WEBSOCKET_CONNECTIONS.set(websocket_connections_count)
            
            await asyncio.sleep(30)  # Update every 30 seconds
        except Exception as e:
            logger.error(f"Metrics update task error: {e}")
            await asyncio.sleep(60)  # Retry after 1 minute on error


def create_app() -> FastAPI:
    """Create and configure the FastAPI application."""
    settings = get_settings()
    
    app = FastAPI(
        title="Gaming Platform - Game Engine Service",
        description="Handles game logic, state management, and rule enforcement",
        version="1.0.0",
        docs_url="/docs" if settings.DEBUG else None,
        redoc_url="/redoc" if settings.DEBUG else None,
        openapi_url="/openapi.json" if settings.DEBUG else None,
        lifespan=lifespan
    )
    
    # Add middleware
    app.add_middleware(
        CORSMiddleware,
        allow_origins=settings.CORS_ORIGINS,
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )
    
    app.add_middleware(
        TrustedHostMiddleware,
        allowed_hosts=settings.ALLOWED_HOSTS
    )
    
    app.add_middleware(RequestIDMiddleware)
    app.add_middleware(SecurityHeadersMiddleware)
    app.add_middleware(RateLimitMiddleware)
    app.add_middleware(LoggingMiddleware)
    
    # Include routers
    app.include_router(
        games.router,
        prefix="/api/v1/games",
        tags=["Games"]
    )
    
    app.include_router(
        moves.router,
        prefix="/api/v1/moves",
        tags=["Moves"]
    )
    
    app.include_router(
        rules.router,
        prefix="/api/v1/rules",
        tags=["Rules"]
    )
    
    return app


# Create app instance
app = create_app()


@app.websocket("/ws/game/{game_id}")
async def websocket_endpoint(websocket: WebSocket, game_id: str):
    """WebSocket endpoint for real-time game communication."""
    websocket_manager = app.state.websocket_manager
    game_manager = app.state.game_manager
    
    await websocket_manager.connect(websocket, game_id)
    
    try:
        while True:
            # Receive message from client
            data = await websocket.receive_json()
            
            # Process game action
            result = await game_manager.process_websocket_action(
                game_id=game_id,
                action=data,
                websocket=websocket
            )
            
            # Broadcast result to all connected clients for this game
            if result:
                await websocket_manager.broadcast_to_game(game_id, result)
                
    except WebSocketDisconnect:
        await websocket_manager.disconnect(websocket, game_id)
        logger.info(f"WebSocket disconnected from game {game_id}")
    except Exception as e:
        logger.error(f"WebSocket error in game {game_id}: {e}")
        await websocket_manager.disconnect(websocket, game_id)


@app.get("/health")
async def health_check():
    """Health check endpoint."""
    try:
        health_checker = app.state.health_checker
        health_status = await health_checker.check_health()
        
        if health_status["status"] == "healthy":
            return health_status
        else:
            raise HTTPException(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                detail=health_status
            )
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail={"status": "unhealthy", "error": str(e)}
        )


@app.get("/metrics")
async def metrics():
    """Prometheus metrics endpoint."""
    try:
        metrics_data = generate_latest()
        return Response(
            content=metrics_data,
            media_type="text/plain"
        )
    except Exception as e:
        logger.error(f"Metrics collection failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Metrics collection failed"
        )


@app.get("/info")
async def service_info():
    """Service information endpoint."""
    settings = get_settings()
    game_manager = app.state.game_manager
    
    return {
        "service": "Game Engine Service",
        "version": "1.0.0",
        "environment": settings.ENVIRONMENT,
        "debug": settings.DEBUG,
        "supported_games": await game_manager.get_supported_games(),
        "features": {
            "real_time_gameplay": True,
            "websocket_support": True,
            "game_state_persistence": True,
            "move_validation": True,
            "game_rules_engine": True,
            "spectator_mode": True,
            "game_replay": True,
            "ai_opponents": False  # Future feature
        },
        "endpoints": {
            "health": "/health",
            "metrics": "/metrics",
            "docs": "/docs" if settings.DEBUG else None,
            "games": "/api/v1/games",
            "moves": "/api/v1/moves",
            "rules": "/api/v1/rules",
            "websocket": "/ws/game/{game_id}"
        }
    }


@app.middleware("http")
async def add_process_time_header(request, call_next):
    """Add request processing time to response headers."""
    import time
    
    start_time = time.time()
    response = await call_next(request)
    process_time = time.time() - start_time
    
    response.headers["X-Process-Time"] = str(process_time)
    
    # Record metrics
    REQUEST_COUNT.labels(
        method=request.method,
        endpoint=request.url.path,
        status=response.status_code
    ).inc()
    
    REQUEST_DURATION.labels(
        method=request.method,
        endpoint=request.url.path
    ).observe(process_time)
    
    return response


if __name__ == "__main__":
    settings = get_settings()
    
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=settings.DEBUG,
        log_level="info",
        access_log=True,
        workers=1 if settings.DEBUG else 4
    )
