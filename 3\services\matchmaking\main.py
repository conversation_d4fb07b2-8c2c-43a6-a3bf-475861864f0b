"""
Matchmaking Service - Main Application
Handles ELO-based player matching, queue management, and game session creation.
"""

import logging
import asyncio
from contextlib import asynccontextmanager
from typing import Dict, Any, List, Optional

import uvicorn
from fastapi import FastAPI, HTTPException, Depends, status, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from prometheus_client import Counter, Histogram, Gauge, generate_latest
from starlette.responses import Response

from .config import get_settings
from .database import get_db, init_db
from .routers import matchmaking, queue, elo
from .middleware import (
    LoggingMiddleware,
    RateLimitMiddleware,
    SecurityHeadersMiddleware,
    RequestIDMiddleware
)
from .utils.health import HealthChecker
from .utils.metrics import MetricsCollector
from .matchmaking_engine import MatchmakingEngine
from .elo_system import ELOSystem

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Metrics
REQUEST_COUNT = Counter(
    'matchmaking_requests_total',
    'Total requests to matchmaking service',
    ['method', 'endpoint', 'status']
)

REQUEST_DURATION = Histogram(
    'matchmaking_request_duration_seconds',
    'Request duration in seconds',
    ['method', 'endpoint']
)

QUEUE_SIZE = Gauge(
    'matchmaking_queue_size',
    'Number of players in matchmaking queue',
    ['game_type', 'skill_bracket']
)

MATCHES_CREATED = Counter(
    'matchmaking_matches_created_total',
    'Total number of matches created',
    ['game_type']
)

AVERAGE_WAIT_TIME = Histogram(
    'matchmaking_wait_time_seconds',
    'Average wait time for matchmaking',
    ['game_type', 'skill_bracket']
)

MATCH_QUALITY = Histogram(
    'matchmaking_quality_score',
    'Quality score of created matches',
    ['game_type']
)


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager."""
    # Startup
    logger.info("Starting Matchmaking Service...")
    
    # Initialize database
    await init_db()
    logger.info("Database initialized")
    
    # Initialize ELO system
    elo_system = ELOSystem()
    app.state.elo_system = elo_system
    logger.info("ELO system initialized")
    
    # Initialize matchmaking engine
    matchmaking_engine = MatchmakingEngine(elo_system)
    app.state.matchmaking_engine = matchmaking_engine
    await matchmaking_engine.initialize()
    logger.info("Matchmaking engine initialized")
    
    # Initialize health checker
    health_checker = HealthChecker()
    app.state.health_checker = health_checker
    
    # Initialize metrics collector
    metrics_collector = MetricsCollector()
    app.state.metrics_collector = metrics_collector
    
    # Start background tasks
    asyncio.create_task(matchmaking_task(matchmaking_engine))
    asyncio.create_task(queue_cleanup_task(matchmaking_engine))
    asyncio.create_task(metrics_update_task(matchmaking_engine))
    asyncio.create_task(elo_recalculation_task(elo_system))
    
    logger.info("Matchmaking Service started successfully")
    
    yield
    
    # Shutdown
    logger.info("Shutting down Matchmaking Service...")
    
    # Cleanup resources
    if hasattr(app.state, 'matchmaking_engine'):
        await app.state.matchmaking_engine.cleanup()
    
    if hasattr(app.state, 'elo_system'):
        await app.state.elo_system.cleanup()
    
    if hasattr(app.state, 'health_checker'):
        await app.state.health_checker.cleanup()
    
    if hasattr(app.state, 'metrics_collector'):
        await app.state.metrics_collector.cleanup()
    
    logger.info("Matchmaking Service shutdown complete")


async def matchmaking_task(matchmaking_engine: MatchmakingEngine):
    """Background task to process matchmaking queue."""
    while True:
        try:
            matches_created = await matchmaking_engine.process_queue()
            
            if matches_created > 0:
                logger.info(f"Created {matches_created} matches")
            
            await asyncio.sleep(5)  # Process queue every 5 seconds
        except Exception as e:
            logger.error(f"Matchmaking task error: {e}")
            await asyncio.sleep(10)  # Retry after 10 seconds on error


async def queue_cleanup_task(matchmaking_engine: MatchmakingEngine):
    """Background task to cleanup expired queue entries."""
    while True:
        try:
            cleaned_entries = await matchmaking_engine.cleanup_expired_queue_entries()
            
            if cleaned_entries > 0:
                logger.info(f"Cleaned up {cleaned_entries} expired queue entries")
            
            await asyncio.sleep(60)  # Run every minute
        except Exception as e:
            logger.error(f"Queue cleanup task error: {e}")
            await asyncio.sleep(60)  # Retry after 1 minute on error


async def metrics_update_task(matchmaking_engine: MatchmakingEngine):
    """Background task to update metrics."""
    while True:
        try:
            # Update queue size metrics
            queue_stats = await matchmaking_engine.get_queue_statistics()
            
            for game_type, brackets in queue_stats.items():
                for bracket, count in brackets.items():
                    QUEUE_SIZE.labels(
                        game_type=game_type,
                        skill_bracket=bracket
                    ).set(count)
            
            await asyncio.sleep(30)  # Update every 30 seconds
        except Exception as e:
            logger.error(f"Metrics update task error: {e}")
            await asyncio.sleep(60)  # Retry after 1 minute on error


async def elo_recalculation_task(elo_system: ELOSystem):
    """Background task to recalculate ELO ratings periodically."""
    while True:
        try:
            # Recalculate ELO ratings for recent games
            await elo_system.recalculate_recent_ratings()
            
            # Update skill brackets
            await elo_system.update_skill_brackets()
            
            await asyncio.sleep(3600)  # Run every hour
        except Exception as e:
            logger.error(f"ELO recalculation task error: {e}")
            await asyncio.sleep(600)  # Retry after 10 minutes on error


def create_app() -> FastAPI:
    """Create and configure the FastAPI application."""
    settings = get_settings()
    
    app = FastAPI(
        title="Gaming Platform - Matchmaking Service",
        description="Handles ELO-based player matching and queue management",
        version="1.0.0",
        docs_url="/docs" if settings.DEBUG else None,
        redoc_url="/redoc" if settings.DEBUG else None,
        openapi_url="/openapi.json" if settings.DEBUG else None,
        lifespan=lifespan
    )
    
    # Add middleware
    app.add_middleware(
        CORSMiddleware,
        allow_origins=settings.CORS_ORIGINS,
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )
    
    app.add_middleware(
        TrustedHostMiddleware,
        allowed_hosts=settings.ALLOWED_HOSTS
    )
    
    app.add_middleware(RequestIDMiddleware)
    app.add_middleware(SecurityHeadersMiddleware)
    app.add_middleware(RateLimitMiddleware)
    app.add_middleware(LoggingMiddleware)
    
    # Include routers
    app.include_router(
        matchmaking.router,
        prefix="/api/v1/matchmaking",
        tags=["Matchmaking"]
    )
    
    app.include_router(
        queue.router,
        prefix="/api/v1/queue",
        tags=["Queue"]
    )
    
    app.include_router(
        elo.router,
        prefix="/api/v1/elo",
        tags=["ELO System"]
    )
    
    return app


# Create app instance
app = create_app()


@app.get("/health")
async def health_check():
    """Health check endpoint."""
    try:
        health_checker = app.state.health_checker
        health_status = await health_checker.check_health()
        
        if health_status["status"] == "healthy":
            return health_status
        else:
            raise HTTPException(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                detail=health_status
            )
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail={"status": "unhealthy", "error": str(e)}
        )


@app.get("/metrics")
async def metrics():
    """Prometheus metrics endpoint."""
    try:
        metrics_data = generate_latest()
        return Response(
            content=metrics_data,
            media_type="text/plain"
        )
    except Exception as e:
        logger.error(f"Metrics collection failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Metrics collection failed"
        )


@app.get("/info")
async def service_info():
    """Service information endpoint."""
    settings = get_settings()
    matchmaking_engine = app.state.matchmaking_engine
    
    return {
        "service": "Matchmaking Service",
        "version": "1.0.0",
        "environment": settings.ENVIRONMENT,
        "debug": settings.DEBUG,
        "features": {
            "elo_based_matching": True,
            "skill_brackets": True,
            "wait_time_optimization": True,
            "queue_management": True,
            "match_quality_scoring": True,
            "regional_matching": True,
            "tournament_support": False  # Future feature
        },
        "elo_settings": {
            "k_factor": settings.ELO_K_FACTOR,
            "default_rating": settings.ELO_DEFAULT_RATING,
            "rating_range": settings.MATCHMAKING_RATING_RANGE,
            "timeout_seconds": settings.MATCHMAKING_TIMEOUT_SECONDS
        },
        "endpoints": {
            "health": "/health",
            "metrics": "/metrics",
            "docs": "/docs" if settings.DEBUG else None,
            "matchmaking": "/api/v1/matchmaking",
            "queue": "/api/v1/queue",
            "elo": "/api/v1/elo"
        }
    }


@app.middleware("http")
async def add_process_time_header(request, call_next):
    """Add request processing time to response headers."""
    import time
    
    start_time = time.time()
    response = await call_next(request)
    process_time = time.time() - start_time
    
    response.headers["X-Process-Time"] = str(process_time)
    
    # Record metrics
    REQUEST_COUNT.labels(
        method=request.method,
        endpoint=request.url.path,
        status=response.status_code
    ).inc()
    
    REQUEST_DURATION.labels(
        method=request.method,
        endpoint=request.url.path
    ).observe(process_time)
    
    return response


if __name__ == "__main__":
    settings = get_settings()
    
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=settings.DEBUG,
        log_level="info",
        access_log=True,
        workers=1 if settings.DEBUG else 4
    )
