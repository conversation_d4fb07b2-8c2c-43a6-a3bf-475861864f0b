"""
MindLink Agent Core - A lightweight AI agent system using LLMs.

This library provides a modular framework for building AI agents using Large Language Models
with a structured JSON-based communication interface (MindLink API).
"""

import os
import shutil
import stat

# Monkey-patch shutil.rmtree to handle Windows permission errors
_orig_rmtree = shutil.rmtree

def _safe_rmtree(path, ignore_errors=False, onerror=None):
    def _onerror(func, p, exc_info):
        try:
            os.chmod(p, 0o777)
        except Exception:
            pass
        try:
            func(p)
        except Exception:
            pass
    return _orig_rmtree(path, ignore_errors=ignore_errors, onerror=_onerror)

shutil.rmtree = _safe_rmtree

__version__ = "0.2.0"

# Import main components for easier access
from .models.llm import LLMInterface
from .models.openrouter import OpenRouterModel, OPENROUTER_MODELS
from .config import DEFAULT_SYSTEM_PROMPT, OPENROUTER_CONFIG
from .executor import ToolExecutor
from .schemas.mindlink import MindLinkRequest
from .main import create_llm
from .config import load_config

def run_tool(tool_name: str, **parameters) -> dict:
    """
    Convenience synchronous tool invocation without server or Celery.
    """
    req = MindLinkRequest(action={"tool_name": tool_name, "parameters": parameters})
    resp = ToolExecutor().execute(req)
    return resp.model_dump()

def run_agent(goal: str, llm_provider: str = None, max_steps: int = None, max_parsing_retries: int = None, retry_delay: float = None) -> tuple:
    """
    One-call agent execution with optional overrides for execution parameters.
    llm_provider: override LLM provider.
    max_steps: optional max steps for planning/execution.
    max_parsing_retries: optional JSON parsing retry limit.
    retry_delay: optional delay between parsing retries.
    """
    # Load default config
    config = load_config(None)
    # Override LLM provider if given
    if llm_provider:
        config["llm"]["provider"] = llm_provider
    # Override agent parameters if provided
    if max_steps is not None:
        config["agent"]["max_steps"] = max_steps
    if max_parsing_retries is not None:
        config["agent"]["max_parsing_retries"] = max_parsing_retries
    if retry_delay is not None:
        config["agent"]["retry_delay"] = retry_delay
    # Initialize LLM and AgentOS
    llm = create_llm(config)
    from mindlink.agent import AgentOS
    agent = AgentOS(
        llm=llm,
        system_prompt_template=config["agent"]["system_prompt_template"],
        max_steps=config["agent"]["max_steps"],
        max_parsing_retries=config["agent"]["max_parsing_retries"],
        retry_delay=config["agent"]["retry_delay"],
        enable_large_project_mode=config["agent"].get("enable_large_project_mode", True),
        large_project_batch_size=config["agent"].get("large_project_batch_size", 10)
    )
    # Run the goal and return (success, result, history)
    return agent.run(goal)

# Define public API
__all__ = [
    'LLMInterface',
    'OpenRouterModel',
    'OPENROUTER_MODELS',
    'DEFAULT_SYSTEM_PROMPT',
    'OPENROUTER_CONFIG',
    'run_tool',
    'run_agent',
]

# Import AgentOS at the end to avoid circular imports
from mindlink.agent import AgentOS
__all__.append('AgentOS')
