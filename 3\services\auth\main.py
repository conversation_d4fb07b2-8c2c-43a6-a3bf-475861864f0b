"""
Authentication Service - Main Application
Handles user authentication, authorization, and JWT token management.
"""

import logging
import os
from contextlib import asynccontextmanager
from typing import Dict, Any

import uvicorn
from fastapi import FastAPI, HTTPException, Depends, status
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from prometheus_client import Counter, Histogram, generate_latest
from starlette.responses import Response

from .config import get_settings
from .database import get_db, init_db
from .routers import auth, users, oauth
from .middleware import (
    LoggingMiddleware,
    RateLimitMiddleware,
    SecurityHeadersMiddleware,
    RequestIDMiddleware
)
from .utils.health import HealthChecker
from .utils.metrics import MetricsCollector

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Metrics
REQUEST_COUNT = Counter(
    'auth_service_requests_total',
    'Total requests to auth service',
    ['method', 'endpoint', 'status']
)

REQUEST_DURATION = Histogram(
    'auth_service_request_duration_seconds',
    'Request duration in seconds',
    ['method', 'endpoint']
)

# Security
security = HTTPBearer()


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager."""
    # Startup
    logger.info("Starting Authentication Service...")
    
    # Initialize database
    await init_db()
    logger.info("Database initialized")
    
    # Initialize health checker
    health_checker = HealthChecker()
    app.state.health_checker = health_checker
    
    # Initialize metrics collector
    metrics_collector = MetricsCollector()
    app.state.metrics_collector = metrics_collector
    
    logger.info("Authentication Service started successfully")
    
    yield
    
    # Shutdown
    logger.info("Shutting down Authentication Service...")
    
    # Cleanup resources
    if hasattr(app.state, 'health_checker'):
        await app.state.health_checker.cleanup()
    
    if hasattr(app.state, 'metrics_collector'):
        await app.state.metrics_collector.cleanup()
    
    logger.info("Authentication Service shutdown complete")


def create_app() -> FastAPI:
    """Create and configure the FastAPI application."""
    settings = get_settings()
    
    app = FastAPI(
        title="Gaming Platform - Authentication Service",
        description="Handles user authentication, authorization, and JWT token management",
        version="1.0.0",
        docs_url="/docs" if settings.DEBUG else None,
        redoc_url="/redoc" if settings.DEBUG else None,
        openapi_url="/openapi.json" if settings.DEBUG else None,
        lifespan=lifespan
    )
    
    # Add middleware
    app.add_middleware(
        CORSMiddleware,
        allow_origins=settings.CORS_ORIGINS,
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )
    
    app.add_middleware(
        TrustedHostMiddleware,
        allowed_hosts=settings.ALLOWED_HOSTS
    )
    
    app.add_middleware(RequestIDMiddleware)
    app.add_middleware(SecurityHeadersMiddleware)
    app.add_middleware(RateLimitMiddleware)
    app.add_middleware(LoggingMiddleware)
    
    # Include routers
    app.include_router(
        auth.router,
        prefix="/api/v1/auth",
        tags=["Authentication"]
    )
    
    app.include_router(
        users.router,
        prefix="/api/v1/users",
        tags=["Users"]
    )
    
    app.include_router(
        oauth.router,
        prefix="/api/v1/oauth",
        tags=["OAuth"]
    )
    
    return app


# Create app instance
app = create_app()


@app.get("/health")
async def health_check():
    """Health check endpoint."""
    try:
        health_checker = app.state.health_checker
        health_status = await health_checker.check_health()
        
        if health_status["status"] == "healthy":
            return health_status
        else:
            raise HTTPException(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                detail=health_status
            )
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail={"status": "unhealthy", "error": str(e)}
        )


@app.get("/metrics")
async def metrics():
    """Prometheus metrics endpoint."""
    try:
        metrics_data = generate_latest()
        return Response(
            content=metrics_data,
            media_type="text/plain"
        )
    except Exception as e:
        logger.error(f"Metrics collection failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Metrics collection failed"
        )


@app.get("/info")
async def service_info():
    """Service information endpoint."""
    settings = get_settings()
    return {
        "service": "Authentication Service",
        "version": "1.0.0",
        "environment": settings.ENVIRONMENT,
        "debug": settings.DEBUG,
        "features": {
            "oauth": True,
            "jwt_refresh": True,
            "rate_limiting": True,
            "audit_logging": True,
            "password_policies": True,
            "account_lockout": True,
            "email_verification": True,
            "two_factor_auth": False  # Future feature
        },
        "endpoints": {
            "health": "/health",
            "metrics": "/metrics",
            "docs": "/docs" if settings.DEBUG else None,
            "auth": "/api/v1/auth",
            "users": "/api/v1/users",
            "oauth": "/api/v1/oauth"
        }
    }


@app.middleware("http")
async def add_process_time_header(request, call_next):
    """Add request processing time to response headers."""
    import time
    
    start_time = time.time()
    response = await call_next(request)
    process_time = time.time() - start_time
    
    response.headers["X-Process-Time"] = str(process_time)
    
    # Record metrics
    REQUEST_COUNT.labels(
        method=request.method,
        endpoint=request.url.path,
        status=response.status_code
    ).inc()
    
    REQUEST_DURATION.labels(
        method=request.method,
        endpoint=request.url.path
    ).observe(process_time)
    
    return response


@app.exception_handler(HTTPException)
async def http_exception_handler(request, exc):
    """Custom HTTP exception handler."""
    logger.warning(
        f"HTTP {exc.status_code} error on {request.method} {request.url.path}: {exc.detail}"
    )
    
    return {
        "error": "HTTP_ERROR",
        "message": exc.detail,
        "status_code": exc.status_code,
        "path": request.url.path,
        "method": request.method
    }


@app.exception_handler(Exception)
async def general_exception_handler(request, exc):
    """General exception handler."""
    logger.error(
        f"Unhandled error on {request.method} {request.url.path}: {exc}",
        exc_info=True
    )
    
    return {
        "error": "INTERNAL_SERVER_ERROR",
        "message": "An internal server error occurred",
        "status_code": 500,
        "path": request.url.path,
        "method": request.method
    }


if __name__ == "__main__":
    settings = get_settings()
    
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=settings.DEBUG,
        log_level="info",
        access_log=True,
        workers=1 if settings.DEBUG else 4
    )
