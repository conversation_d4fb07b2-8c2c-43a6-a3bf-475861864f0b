"""
Game Models - Database and Pydantic models for game management.
Handles games, moves, rules, and game state management.
"""

from datetime import datetime, timed<PERSON>ta
from typing import Optional, List, Dict, Any, Union
from uuid import UUID
from enum import Enum

from sqlalchemy import Column, String, Boolean, Integer, DateTime, Text, ForeignKey, Float
from sqlalchemy.dialects.postgresql import UUID as PostgresUUID, JSON<PERSON>, ARRAY
from sqlalchemy.orm import relationship
from pydantic import BaseModel, Field, validator

from .base import Base, BaseSchema, BaseCreateSchema, BaseUpdateSchema, BaseResponseSchema, AuditLogMixin


class GameType(str, Enum):
    """Game type enumeration."""
    TIC_TAC_TOE = "tic_tac_toe"
    CONNECT_FOUR = "connect_four"
    REVERSI = "reversi"
    GOMOKU = "gomoku"
    CHESS = "chess"  # Future expansion
    CHECKERS = "checkers"  # Future expansion


class GameStatus(str, Enum):
    """Game status enumeration."""
    WAITING = "waiting"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    CANCELLED = "cancelled"
    ABANDONED = "abandoned"
    PAUSED = "paused"


class GameResult(str, Enum):
    """Game result enumeration."""
    PLAYER1_WIN = "player1_win"
    PLAYER2_WIN = "player2_win"
    DRAW = "draw"
    ABANDONED = "abandoned"
    CANCELLED = "cancelled"


class MoveType(str, Enum):
    """Move type enumeration."""
    PLACE = "place"
    MOVE = "move"
    CAPTURE = "capture"
    SPECIAL = "special"
    RESIGN = "resign"
    DRAW_OFFER = "draw_offer"
    DRAW_ACCEPT = "draw_accept"
    DRAW_DECLINE = "draw_decline"


class Game(Base, AuditLogMixin):
    """Game database model."""
    
    __tablename__ = "games"
    
    # Game Information
    game_type = Column(String(50), nullable=False, index=True)
    status = Column(String(20), default=GameStatus.WAITING, nullable=False, index=True)
    result = Column(String(20), nullable=True)
    
    # Players
    player1_id = Column(PostgresUUID(as_uuid=True), ForeignKey('users.id'), nullable=False, index=True)
    player2_id = Column(PostgresUUID(as_uuid=True), ForeignKey('users.id'), nullable=True, index=True)
    current_player_id = Column(PostgresUUID(as_uuid=True), ForeignKey('users.id'), nullable=True)
    winner_id = Column(PostgresUUID(as_uuid=True), ForeignKey('users.id'), nullable=True)
    
    # Game State
    board_state = Column(JSONB, default=dict, nullable=False)
    game_config = Column(JSONB, default=dict, nullable=False)
    move_count = Column(Integer, default=0, nullable=False)
    
    # Timing
    time_limit_minutes = Column(Integer, nullable=True)
    player1_time_remaining = Column(Integer, nullable=True)  # seconds
    player2_time_remaining = Column(Integer, nullable=True)  # seconds
    last_move_at = Column(DateTime(timezone=True), nullable=True)
    
    # Game Duration
    started_at = Column(DateTime(timezone=True), nullable=True)
    completed_at = Column(DateTime(timezone=True), nullable=True)
    
    # ELO Changes
    player1_elo_before = Column(Integer, nullable=True)
    player1_elo_after = Column(Integer, nullable=True)
    player2_elo_before = Column(Integer, nullable=True)
    player2_elo_after = Column(Integer, nullable=True)
    
    # Spectators and Features
    spectator_count = Column(Integer, default=0, nullable=False)
    allow_spectators = Column(Boolean, default=True, nullable=False)
    is_ranked = Column(Boolean, default=True, nullable=False)
    is_tournament = Column(Boolean, default=False, nullable=False)
    tournament_id = Column(PostgresUUID(as_uuid=True), nullable=True)
    
    # Game Metadata
    metadata = Column(JSONB, default=dict, nullable=False)
    
    # Relationships
    player1 = relationship("User", foreign_keys=[player1_id], back_populates="games_as_player1")
    player2 = relationship("User", foreign_keys=[player2_id], back_populates="games_as_player2")
    current_player = relationship("User", foreign_keys=[current_player_id])
    winner = relationship("User", foreign_keys=[winner_id])
    moves = relationship("GameMove", back_populates="game", cascade="all, delete-orphan")
    
    @property
    def duration(self) -> Optional[timedelta]:
        """Calculate game duration."""
        if self.started_at and self.completed_at:
            return self.completed_at - self.started_at
        elif self.started_at:
            return datetime.utcnow() - self.started_at
        return None
    
    @property
    def is_active(self) -> bool:
        """Check if game is active."""
        return self.status in [GameStatus.WAITING, GameStatus.IN_PROGRESS, GameStatus.PAUSED]
    
    @property
    def is_finished(self) -> bool:
        """Check if game is finished."""
        return self.status in [GameStatus.COMPLETED, GameStatus.CANCELLED, GameStatus.ABANDONED]
    
    def get_opponent(self, player_id: UUID) -> Optional[UUID]:
        """Get opponent player ID."""
        if self.player1_id == player_id:
            return self.player2_id
        elif self.player2_id == player_id:
            return self.player1_id
        return None
    
    def is_player_turn(self, player_id: UUID) -> bool:
        """Check if it's the player's turn."""
        return self.current_player_id == player_id
    
    def can_make_move(self, player_id: UUID) -> bool:
        """Check if player can make a move."""
        return (
            self.status == GameStatus.IN_PROGRESS and
            self.is_player_turn(player_id) and
            player_id in [self.player1_id, self.player2_id]
        )
    
    def start_game(self):
        """Start the game."""
        self.status = GameStatus.IN_PROGRESS
        self.started_at = datetime.utcnow()
        self.current_player_id = self.player1_id  # Player 1 starts
    
    def complete_game(self, result: GameResult, winner_id: Optional[UUID] = None):
        """Complete the game."""
        self.status = GameStatus.COMPLETED
        self.result = result
        self.winner_id = winner_id
        self.completed_at = datetime.utcnow()
    
    def abandon_game(self):
        """Abandon the game."""
        self.status = GameStatus.ABANDONED
        self.result = GameResult.ABANDONED
        self.completed_at = datetime.utcnow()


class GameMove(Base, AuditLogMixin):
    """Game move database model."""
    
    __tablename__ = "game_moves"
    
    # Move Information
    game_id = Column(PostgresUUID(as_uuid=True), ForeignKey('games.id'), nullable=False, index=True)
    player_id = Column(PostgresUUID(as_uuid=True), ForeignKey('users.id'), nullable=False, index=True)
    move_number = Column(Integer, nullable=False)
    move_type = Column(String(20), default=MoveType.PLACE, nullable=False)
    
    # Move Data
    move_data = Column(JSONB, nullable=False)  # Position, piece, etc.
    board_state_before = Column(JSONB, nullable=True)
    board_state_after = Column(JSONB, nullable=False)
    
    # Timing
    time_taken = Column(Float, nullable=True)  # seconds
    time_remaining = Column(Integer, nullable=True)  # seconds
    
    # Validation
    is_valid = Column(Boolean, default=True, nullable=False)
    validation_errors = Column(ARRAY(String), nullable=True)
    
    # Move Metadata
    metadata = Column(JSONB, default=dict, nullable=False)
    
    # Relationships
    game = relationship("Game", back_populates="moves")
    player = relationship("User")
    
    def __repr__(self):
        return f"<GameMove(game_id={self.game_id}, player_id={self.player_id}, move_number={self.move_number})>"


class GameRule(Base):
    """Game rule database model."""
    
    __tablename__ = "game_rules"
    
    # Rule Information
    game_type = Column(String(50), nullable=False, index=True)
    rule_name = Column(String(100), nullable=False)
    rule_description = Column(Text, nullable=True)
    
    # Rule Configuration
    rule_config = Column(JSONB, nullable=False)
    is_active = Column(Boolean, default=True, nullable=False)
    priority = Column(Integer, default=0, nullable=False)
    
    # Versioning
    version = Column(String(20), default="1.0", nullable=False)
    
    def __repr__(self):
        return f"<GameRule(game_type={self.game_type}, rule_name={self.rule_name})>"


# Pydantic Schemas

class GameCreateSchema(BaseCreateSchema):
    """Schema for creating a new game."""
    
    game_type: GameType = Field(...)
    player2_id: Optional[UUID] = Field(None, description="Second player ID (for direct challenges)")
    time_limit_minutes: Optional[int] = Field(None, ge=1, le=180, description="Time limit in minutes")
    allow_spectators: bool = Field(default=True)
    is_ranked: bool = Field(default=True)
    game_config: Optional[Dict[str, Any]] = Field(default_factory=dict)


class GameJoinSchema(BaseSchema):
    """Schema for joining a game."""
    
    game_id: UUID = Field(...)


class GameMoveSchema(BaseCreateSchema):
    """Schema for making a game move."""
    
    game_id: UUID = Field(...)
    move_type: MoveType = Field(default=MoveType.PLACE)
    move_data: Dict[str, Any] = Field(..., description="Move-specific data (position, piece, etc.)")
    
    @validator('move_data')
    def validate_move_data(cls, v):
        """Validate move data is not empty."""
        if not v:
            raise ValueError('Move data cannot be empty')
        return v


class GameResponseSchema(BaseResponseSchema):
    """Schema for game API responses."""
    
    game_type: GameType
    status: GameStatus
    result: Optional[GameResult]
    player1_id: UUID
    player2_id: Optional[UUID]
    current_player_id: Optional[UUID]
    winner_id: Optional[UUID]
    board_state: Dict[str, Any]
    move_count: int
    time_limit_minutes: Optional[int]
    player1_time_remaining: Optional[int]
    player2_time_remaining: Optional[int]
    started_at: Optional[datetime]
    completed_at: Optional[datetime]
    duration_seconds: Optional[float]
    spectator_count: int
    allow_spectators: bool
    is_ranked: bool
    is_tournament: bool


class GameMoveResponseSchema(BaseResponseSchema):
    """Schema for game move API responses."""
    
    game_id: UUID
    player_id: UUID
    move_number: int
    move_type: MoveType
    move_data: Dict[str, Any]
    time_taken: Optional[float]
    time_remaining: Optional[int]
    is_valid: bool
    validation_errors: Optional[List[str]]


class GameListSchema(BaseSchema):
    """Schema for game list responses."""
    
    id: UUID
    game_type: GameType
    status: GameStatus
    player1_id: UUID
    player2_id: Optional[UUID]
    move_count: int
    started_at: Optional[datetime]
    is_ranked: bool
    spectator_count: int


class GameStatsSchema(BaseSchema):
    """Schema for game statistics."""
    
    total_games: int
    active_games: int
    completed_games: int
    games_by_type: Dict[str, int]
    average_game_duration: float
    most_popular_game: str
    peak_concurrent_games: int


class GameRuleSchema(BaseResponseSchema):
    """Schema for game rule responses."""
    
    game_type: GameType
    rule_name: str
    rule_description: Optional[str]
    rule_config: Dict[str, Any]
    is_active: bool
    priority: int
    version: str


class GameConfigSchema(BaseSchema):
    """Schema for game configuration."""
    
    board_size: Optional[int] = Field(None, ge=3, le=20)
    win_condition: Optional[int] = Field(None, ge=3, le=10)
    time_limit: Optional[int] = Field(None, ge=60, le=7200)  # seconds
    allow_undo: bool = Field(default=False)
    show_hints: bool = Field(default=False)
    difficulty: Optional[str] = Field(None, regex="^(easy|medium|hard)$")


class SpectatorJoinSchema(BaseSchema):
    """Schema for joining as spectator."""
    
    game_id: UUID = Field(...)


class GameSearchSchema(BaseSchema):
    """Schema for game search parameters."""
    
    game_type: Optional[GameType] = Field(None)
    status: Optional[GameStatus] = Field(None)
    player_id: Optional[UUID] = Field(None)
    is_ranked: Optional[bool] = Field(None)
    created_after: Optional[datetime] = Field(None)
    created_before: Optional[datetime] = Field(None)
