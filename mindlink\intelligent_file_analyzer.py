"""
Intelligent File Analysis Module for MindLink Agent

This module provides LLM-based analysis for understanding project requirements,
determining file structures, and making intelligent architectural decisions
without rigid regex patterns or artificial limitations.
"""

import logging
from typing import Dict, List, Any, Optional, Union
from dataclasses import dataclass
import json

logger = logging.getLogger(__name__)


@dataclass
class ProjectAnalysis:
    """Structured analysis result from LLM-based project understanding."""
    is_multi_file: bool
    estimated_files: int
    project_type: str
    project_description: str
    file_structure: List[Dict[str, Any]]
    lines_per_file: Optional[int]
    complexity_level: str
    recommended_architecture: str
    technology_stack: List[str]
    confidence_score: float


class IntelligentFileAnalyzer:
    """
    LLM-powered analyzer that understands natural language project requests
    and makes intelligent decisions about file structure and architecture.
    """
    
    def __init__(self, llm_interface):
        """Initialize with LLM interface for intelligent analysis."""
        self.llm = llm_interface
        
    def analyze_project_request(self, user_request: str) -> Optional[ProjectAnalysis]:
        """
        Analyze a user request using LLM to understand project requirements.
        
        Args:
            user_request: Natural language description of what to create
            
        Returns:
            ProjectAnalysis object with intelligent recommendations
        """
        try:
            analysis_prompt = self._build_analysis_prompt(user_request)
            
            # Get LLM analysis
            response = self.llm.generate(
                system_prompt=self._get_analysis_system_prompt(),
                user_prompt=analysis_prompt
            )
            
            # Parse LLM response into structured analysis
            return self._parse_analysis_response(response, user_request)
            
        except Exception as e:
            logger.error(f"Error in LLM-based project analysis: {e}")
            return None
    
    def _get_analysis_system_prompt(self) -> str:
        """Get the system prompt for project analysis."""
        return """You are an expert software architect and project analyst. Your task is to analyze natural language requests and provide intelligent recommendations for project structure.

You excel at:
- Understanding project scope and complexity from natural language
- Determining appropriate file structures and architectures
- Making creative and contextually appropriate naming decisions
- Estimating realistic project sizes without artificial constraints
- Recommending modern development practices and patterns

Respond with a JSON object containing your analysis. Be creative, intelligent, and contextually aware. There are no artificial limits on file counts - recommend what makes sense for the project."""

    def _build_analysis_prompt(self, user_request: str) -> str:
        """Build the analysis prompt for the LLM optimized for large projects."""
        return f"""Analyze this project request and provide intelligent recommendations for potentially large-scale projects (up to 100 files):

REQUEST: {user_request}

Provide a JSON response with this structure:
{{
    "is_multi_file": boolean,
    "estimated_files": number (can be up to 100),
    "project_type": "descriptive project type",
    "project_description": "comprehensive description of what this project does",
    "file_structure": [
        {{
            "filename": "intelligent_name.py",
            "purpose": "detailed description of what this file does",
            "estimated_lines": number,
            "dependencies": ["list", "of", "dependencies"],
            "category": "core|config|api|database|frontend|backend|test|docs|deployment",
            "priority": number (1-5, where 1 is highest priority for creation order)
        }}
    ],
    "lines_per_file": number_or_null,
    "complexity_level": "simple|moderate|complex|enterprise|large_scale",
    "recommended_architecture": "detailed architectural pattern recommendation",
    "technology_stack": ["comprehensive", "list", "of", "technologies"],
    "confidence_score": 0.0_to_1.0
}}

Guidelines for large projects:
- Handle projects with 10-100 files efficiently
- Include all necessary components: configs, tests, docs, deployment
- Create logical file organization and dependencies
- Consider microservices, databases, APIs, frontend/backend separation
- Include infrastructure files (Docker, CI/CD, monitoring)
- Generate meaningful, professional filenames following conventions
- Provide detailed file purposes and architectural relationships
- Estimate realistic complexity and scope for production systems
- Consider scalability, security, and maintainability requirements"""

    def _parse_analysis_response(self, response: str, original_request: str) -> Optional[ProjectAnalysis]:
        """Parse LLM response into ProjectAnalysis object."""
        try:
            # Clean response and extract JSON
            cleaned_response = response.strip()
            if cleaned_response.startswith('```json'):
                cleaned_response = cleaned_response[7:]
            if cleaned_response.endswith('```'):
                cleaned_response = cleaned_response[:-3]
            
            analysis_data = json.loads(cleaned_response)
            
            return ProjectAnalysis(
                is_multi_file=analysis_data.get('is_multi_file', False),
                estimated_files=analysis_data.get('estimated_files', 1),
                project_type=analysis_data.get('project_type', 'project'),
                project_description=analysis_data.get('project_description', original_request),
                file_structure=analysis_data.get('file_structure', []),
                lines_per_file=analysis_data.get('lines_per_file'),
                complexity_level=analysis_data.get('complexity_level', 'moderate'),
                recommended_architecture=analysis_data.get('recommended_architecture', 'modular'),
                technology_stack=analysis_data.get('technology_stack', ['python']),
                confidence_score=analysis_data.get('confidence_score', 0.8)
            )
            
        except (json.JSONDecodeError, KeyError) as e:
            logger.error(f"Error parsing LLM analysis response: {e}")
            logger.debug(f"Raw response: {response}")
            
            # Fallback: create basic analysis
            return self._create_fallback_analysis(original_request)
    
    def _create_fallback_analysis(self, request: str) -> ProjectAnalysis:
        """Create a basic fallback analysis when LLM parsing fails, optimized for large projects."""
        request_lower = request.lower()

        # Enhanced heuristics for large project detection
        is_multi = any(word in request_lower for word in [
            'project', 'system', 'application', 'multiple', 'several', 'files',
            'platform', 'microservices', 'scalable', 'enterprise', 'comprehensive'
        ])

        # Estimate file count based on keywords
        estimated_files = 1
        if any(word in request_lower for word in ['platform', 'microservices', 'scalable', 'enterprise']):
            estimated_files = 25  # Large project
        elif any(word in request_lower for word in ['system', 'application', 'comprehensive']):
            estimated_files = 10  # Medium-large project
        elif is_multi:
            estimated_files = 5   # Small multi-file project

        # Determine complexity level
        complexity = 'moderate'
        if any(word in request_lower for word in ['enterprise', 'scalable', 'microservices', 'production']):
            complexity = 'enterprise'
        elif any(word in request_lower for word in ['platform', 'system', 'comprehensive']):
            complexity = 'complex'

        return ProjectAnalysis(
            is_multi_file=is_multi,
            estimated_files=estimated_files,
            project_type='software project',
            project_description=request,
            file_structure=[],
            lines_per_file=None,
            complexity_level=complexity,
            recommended_architecture='modular',
            technology_stack=['python'],
            confidence_score=0.6
        )

    def generate_intelligent_filename(self, 
                                    project_context: Dict[str, Any], 
                                    file_index: int, 
                                    file_purpose: str = None) -> str:
        """
        Generate contextually appropriate filename using LLM intelligence.
        
        Args:
            project_context: Context about the overall project
            file_index: Index of this file in the project
            file_purpose: Specific purpose of this file
            
        Returns:
            Intelligent filename suggestion
        """
        try:
            filename_prompt = self._build_filename_prompt(project_context, file_index, file_purpose)
            
            response = self.llm.generate(
                system_prompt="You are an expert at creating meaningful, professional filenames that follow best practices and conventions.",
                user_prompt=filename_prompt
            )
            
            # Clean and validate filename
            suggested_name = response.strip().strip('"\'`')
            
            # Basic validation
            if self._is_valid_filename(suggested_name):
                return suggested_name
            else:
                return self._generate_fallback_filename(project_context, file_index)
                
        except Exception as e:
            logger.error(f"Error generating intelligent filename: {e}")
            return self._generate_fallback_filename(project_context, file_index)
    
    def _build_filename_prompt(self, context: Dict[str, Any], index: int, purpose: str) -> str:
        """Build prompt for intelligent filename generation."""
        project_type = context.get('file_description_base', 'project file')
        original_goal = context.get('original_goal', '')
        
        prompt = f"""Generate a professional, meaningful filename for this project file:

PROJECT CONTEXT: {original_goal}
PROJECT TYPE: {project_type}
FILE INDEX: {index}
FILE PURPOSE: {purpose or 'general project file'}

Requirements:
- Use professional naming conventions
- Be descriptive and meaningful
- Include appropriate file extension
- Follow modern development practices
- Consider the file's role in the overall project

Respond with ONLY the filename, nothing else."""
        
        return prompt
    
    def _is_valid_filename(self, filename: str) -> bool:
        """Validate if a filename is acceptable."""
        import re
        # Basic filename validation
        return bool(re.match(r'^[a-zA-Z0-9_/.-]+\.[a-zA-Z0-9]+$', filename))
    
    def _generate_fallback_filename(self, context: Dict[str, Any], index: int) -> str:
        """Generate fallback filename when LLM generation fails."""
        project_type = context.get('file_description_base', 'project')
        
        # Intelligent fallback based on context
        base_name = project_type.lower().replace(' ', '_').replace('-', '_')
        
        # Determine extension intelligently
        if 'python' in project_type.lower() or 'py' in project_type.lower():
            extension = '.py'
        elif 'javascript' in project_type.lower() or 'js' in project_type.lower():
            extension = '.js'
        elif 'web' in project_type.lower() or 'html' in project_type.lower():
            extension = '.html'
        elif 'style' in project_type.lower() or 'css' in project_type.lower():
            extension = '.css'
        elif 'config' in project_type.lower():
            extension = '.json'
        elif 'doc' in project_type.lower() or 'readme' in project_type.lower():
            extension = '.md'
        else:
            extension = '.py'  # Default to Python
        
        return f"{base_name}_{index}{extension}"
