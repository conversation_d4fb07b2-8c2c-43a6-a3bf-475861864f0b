# Core Framework
fastapi==0.104.1
uvicorn[standard]==0.24.0
pydantic==2.5.0
pydantic-settings==2.1.0

# Database
sqlalchemy==2.0.23
alembic==1.13.0
asyncpg==0.29.0
psycopg2-binary==2.9.9

# Redis & Caching
redis==5.0.1
aioredis==2.0.1

# Message Queue
aio-pika==9.3.1
celery==5.3.4

# Authentication & Security
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4
python-multipart==0.0.6
bcrypt==4.1.2

# HTTP Client
httpx==0.25.2
aiohttp==3.9.1

# WebSocket Support
websockets==12.0

# Validation & Serialization
email-validator==2.1.0
python-dateutil==2.8.2

# Monitoring & Logging
prometheus-client==0.19.0
structlog==23.2.0
python-json-logger==2.0.7

# Testing
pytest==7.4.3
pytest-asyncio==0.21.1
pytest-cov==4.1.0
httpx==0.25.2
factory-boy==3.3.0

# Development Tools
black==23.11.0
flake8==6.1.0
mypy==1.7.1
isort==5.12.0
pre-commit==3.6.0

# Documentation
mkdocs==1.5.3
mkdocs-material==9.4.8

# Environment Management
python-dotenv==1.0.0

# Utilities
click==8.1.7
rich==13.7.0
typer==0.9.0

# Game Logic
numpy==1.25.2

# Admin Dashboard
jinja2==3.1.2
aiofiles==23.2.1

# Rate Limiting
slowapi==0.1.9

# CORS
fastapi-cors==0.0.6

# Health Checks
fastapi-health==0.4.0

# Metrics
fastapi-prometheus==0.1.0

# Background Tasks
rq==1.15.1

# Configuration
dynaconf==3.2.4

# Timezone Support
pytz==2023.3

# UUID Support
uuid==1.30

# JSON Web Tokens
pyjwt==2.8.0

# Password Hashing
argon2-cffi==23.1.0

# API Documentation
fastapi-users==12.1.2

# Database Connection Pooling
sqlalchemy-pool==0.1.0

# Async Support
asyncio-mqtt==0.16.1

# File Upload
python-multipart==0.0.6

# Image Processing (for avatars)
pillow==10.1.0

# Data Validation
cerberus==1.3.5

# Caching
cachetools==5.3.2

# Serialization
orjson==3.9.10

# HTTP Status Codes
http-status==0.1.0

# Dependency Injection
dependency-injector==4.41.0

# Background Jobs
dramatiq==1.15.0

# Distributed Locking
redlock-py==1.0.8

# Circuit Breaker
pybreaker==1.0.1

# Retry Logic
tenacity==8.2.3

# Load Balancing
haproxy-stats==2.2.0

# Service Discovery
consul-python==1.1.0

# Tracing
opentelemetry-api==1.21.0
opentelemetry-sdk==1.21.0

# Profiling
py-spy==0.3.14

# Memory Profiling
memory-profiler==0.61.0

# Performance Monitoring
psutil==5.9.6

# Kubernetes Client
kubernetes==28.1.0

# Docker SDK
docker==6.1.3

# AWS SDK (if using AWS)
boto3==1.34.0

# Google Cloud SDK (if using GCP)
google-cloud-storage==2.10.0

# Azure SDK (if using Azure)
azure-storage-blob==12.19.0
